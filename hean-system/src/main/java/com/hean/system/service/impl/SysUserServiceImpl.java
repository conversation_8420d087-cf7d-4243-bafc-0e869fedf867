package com.hean.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Validator;

import com.hean.common.service.CipherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.hean.common.annotation.DataScope;
import com.hean.common.constant.UserConstants;
import com.hean.common.core.domain.entity.SysRole;
import com.hean.common.core.domain.entity.SysUser;
import com.hean.common.exception.ServiceException;
import com.hean.common.utils.SecurityUtils;
import com.hean.common.utils.StringUtils;
import com.hean.common.utils.bean.BeanValidators;
import com.hean.common.utils.spring.SpringUtils;
import com.hean.system.domain.SysPost;
import com.hean.system.domain.SysUserPost;
import com.hean.system.domain.SysUserRole;
import com.hean.system.mapper.SysPostMapper;
import com.hean.system.mapper.SysRoleMapper;
import com.hean.system.mapper.SysUserMapper;
import com.hean.system.mapper.SysUserPostMapper;
import com.hean.system.mapper.SysUserRoleMapper;
import com.hean.system.service.ISysConfigService;
import com.hean.system.service.ISysUserService;

/**
 * 用户 业务层处理

 */
@Service
@Slf4j
public class SysUserServiceImpl implements ISysUserService {

//    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private CipherService cipherService;

    @Resource
    private SysRoleMapper roleMapper;

    @Resource
    private SysPostMapper postMapper;

    @Resource
    private SysUserRoleMapper userRoleMapper;

    @Resource
    private SysUserPostMapper userPostMapper;

    @Resource
    private ISysConfigService configService;

    @Resource
    protected Validator validator;

    /**
     * 根据条件分页查询用户列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {

        if(StringUtils.isNotBlank(user.getUserName())) {
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
        }

        return userMapper.selectUserList(user).stream().peek(m -> {
            log.info("查询用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", m.getUserName(), m.getNickName(), m.getPhonenumber());

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            if(StringUtils.isNotBlank(m.getUserName())) {
                log.info("查询用户 --> 解密用户名密文：{}", m.getUserName());
                m.setUserName(cipherService.sm4Decrypt(m.getUserName()));
                log.info("查询用户 --> 解密后用户名明文：{}", m.getUserName());
            }

            if(StringUtils.isNotBlank(m.getNickName())) {
                log.info("查询用户 --> 解密昵称密文：{}", m.getNickName());
                m.setNickName(cipherService.sm4Decrypt(m.getNickName()));
                log.info("查询用户 --> 解密后昵称明文：{}", m.getNickName());
            }

            if(StringUtils.isNotBlank(m.getPhonenumber())) {
                log.info("查询用户 --> 解密手机号密文：{}", m.getPhonenumber());
                m.setPhonenumber(cipherService.sm4Decrypt(m.getPhonenumber()));
                log.info("查询用户 --> 解密后手机号明文：{}", m.getPhonenumber());
            }
        }).collect(Collectors.toList());
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        if(StringUtils.isNotBlank(user.getUserName())) {
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
        }

        return userMapper.selectAllocatedList(user).stream().peek(m -> {
            log.info("查询已分配用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", m.joinItemHash(), m.getItemHash());

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            if(StringUtils.isNotBlank(m.getUserName())) {
                log.info("查询已分配用户 --> 解密用户名密文：{}", m.getUserName());
                m.setUserName(cipherService.sm4Decrypt(m.getUserName()));
                log.info("查询已分配用户 --> 解密后用户名明文：{}", m.getUserName());
            }

            if(StringUtils.isNotBlank(m.getNickName())) {
                log.info("查询已分配用户 --> 解密昵称密文：{}", m.getNickName());
                m.setNickName(cipherService.sm4Decrypt(m.getNickName()));
                log.info("查询已分配用户 --> 解密后昵称明文：{}", m.getNickName());
            }

            if(StringUtils.isNotBlank(m.getPhonenumber())) {
                log.info("查询已分配用户 --> 解密手机号密文：{}", m.getPhonenumber());
                m.setPhonenumber(cipherService.sm4Decrypt(m.getPhonenumber()));
                log.info("查询已分配用户 --> 解密后手机号明文：{}", m.getPhonenumber());
            }
        }).collect(Collectors.toList());
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        if(StringUtils.isNotBlank(user.getUserName())) {
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
        }

        return userMapper.selectUnallocatedList(user).stream().peek(m -> {
            log.info("查询未分配用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", m.joinItemHash(), m.getItemHash());

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            if(StringUtils.isNotBlank(m.getUserName())) {
                log.info("查询未分配用户 --> 解密用户名密文：{}", m.getUserName());
                m.setUserName(cipherService.sm4Decrypt(m.getUserName()));
                log.info("查询未分配用户 --> 解密后用户名明文：{}", m.getUserName());
            }

            if(StringUtils.isNotBlank(m.getNickName())) {
                log.info("查询未分配用户 --> 解密昵称密文：{}", m.getNickName());
                m.setNickName(cipherService.sm4Decrypt(m.getNickName()));
                log.info("查询未分配用户 --> 解密后昵称明文：{}", m.getNickName());
            }

            if(StringUtils.isNotBlank(m.getPhonenumber())) {
                log.info("查询未分配用户 --> 解密手机号密文：{}", m.getPhonenumber());
                m.setPhonenumber(cipherService.sm4Decrypt(m.getPhonenumber()));
                log.info("查询未分配用户 --> 解密后手机号明文：{}", m.getPhonenumber());
            }
        }).collect(Collectors.toList());
    }

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        if(StringUtils.isBlank(userName)) {
            return null;
        }

        SysUser sysUser = userMapper.selectUserByUserName(cipherService.sm4Encrypt(userName));
        if(sysUser == null) {
            return null;
        }

        log.info("通过用户名查询用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", sysUser.joinItemHash(), sysUser.getItemHash());

        // 核验商密密文HASH
        cipherService.checkHash(sysUser.joinItemHash(), sysUser.getItemHash());

        if(StringUtils.isNotBlank(sysUser.getUserName())) {
            log.info("通过用户名查询用户 --> 解密用户名密文：{}", sysUser.getUserName());
            sysUser.setUserName(cipherService.sm4Decrypt(sysUser.getUserName()));
            log.info("通过用户名查询用户 --> 解密后用户名明文：{}", sysUser.getUserName());
        }

        if(StringUtils.isNotBlank(sysUser.getNickName())) {
            log.info("通过用户名查询用户 --> 解密昵称密文：{}", sysUser.getNickName());
            sysUser.setNickName(cipherService.sm4Decrypt(sysUser.getNickName()));
            log.info("通过用户名查询用户 --> 解密后昵称明文：{}", sysUser.getNickName());
        }

        if(StringUtils.isNotBlank(sysUser.getPhonenumber())) {
            log.info("通过用户名查询用户 --> 解密手机号密文：{}", sysUser.getPhonenumber());
            sysUser.setPhonenumber(cipherService.sm4Decrypt(sysUser.getPhonenumber()));
            log.info("通过用户名查询用户 --> 解密后手机号明文：{}", sysUser.getPhonenumber());
        }

        return sysUser;
    }

    @Override
    public SysUser selectUserByOauthCode(String oauthCode) {
        SysUser sysUser = userMapper.selectUserByOauthCode(oauthCode);

        if(sysUser == null) {
            return null;
        }

        log.info("通过oauthCode查询用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", sysUser.joinItemHash(), sysUser.getItemHash());

        // 核验商密密文HASH
        cipherService.checkHash(sysUser.joinItemHash(), sysUser.getItemHash());

        if(StringUtils.isNotBlank(sysUser.getUserName())) {
            log.info("通过oauthCode查询用户 --> 解密用户名密文：{}", sysUser.getUserName());
            sysUser.setUserName(cipherService.sm4Decrypt(sysUser.getUserName()));
            log.info("通过oauthCode查询用户 --> 解密后用户名明文：{}", sysUser.getUserName());
        }

        if(StringUtils.isNotBlank(sysUser.getNickName())) {
            log.info("通过oauthCode查询用户 --> 解密昵称密文：{}", sysUser.getNickName());
            sysUser.setNickName(cipherService.sm4Decrypt(sysUser.getNickName()));
            log.info("通过oauthCode查询用户 --> 解密后昵称明文：{}", sysUser.getNickName());
        }

        if(StringUtils.isNotBlank(sysUser.getPhonenumber())) {
            log.info("通过oauthCode查询用户 --> 解密手机号密文：{}", sysUser.getPhonenumber());
            sysUser.setPhonenumber(cipherService.sm4Decrypt(sysUser.getPhonenumber()));
            log.info("通过oauthCode查询用户 --> 解密后手机号明文：{}", sysUser.getPhonenumber());
        }

        return sysUser;
    }

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {

        SysUser sysUser = userMapper.selectUserById(userId);

        if(sysUser == null) {
            return null;
        }

        log.info("通过用户ID查询用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", sysUser.joinItemHash(), sysUser.getItemHash());

        // 核验商密密文HASH
        cipherService.checkHash(sysUser.joinItemHash(), sysUser.getItemHash());

        if(StringUtils.isNotBlank(sysUser.getUserName())) {
            log.info("通过用户ID查询用户 --> 解密用户名密文：{}", sysUser.getUserName());
            sysUser.setUserName(cipherService.sm4Decrypt(sysUser.getUserName()));
            log.info("通过用户ID查询用户 --> 解密后用户名明文：{}", sysUser.getUserName());
        }

        if(StringUtils.isNotBlank(sysUser.getNickName())) {
            log.info("通过用户ID查询用户 --> 解密昵称密文：{}", sysUser.getNickName());
            sysUser.setNickName(cipherService.sm4Decrypt(sysUser.getNickName()));
            log.info("通过用户ID查询用户 --> 解密后昵称明文：{}", sysUser.getNickName());
        }

        if(StringUtils.isNotBlank(sysUser.getPhonenumber())) {
            log.info("通过用户ID查询用户 --> 解密手机号密文：{}", sysUser.getPhonenumber());
            sysUser.setPhonenumber(cipherService.sm4Decrypt(sysUser.getPhonenumber()));
            log.info("通过用户ID查询用户 --> 解密后手机号明文：{}", sysUser.getPhonenumber());
        }

        return sysUser;
    }

    /**
     * 查询用户所属角色组
     * 
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        if (StringUtils.isBlank(userName)) {
            return StringUtils.EMPTY;
        }

        List<SysRole> list = roleMapper.selectRolesByUserName(cipherService.sm4Encrypt(userName));
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }

        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     * 
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        if (StringUtils.isBlank(userName)) {
            return StringUtils.EMPTY;
        }

        List<SysPost> list = postMapper.selectPostsByUserName(cipherService.sm4Encrypt(userName));
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(cipherService.sm4Encrypt(user.getUserName()));
        if (StringUtils.isNotNull(info) && info.getUserId() != userId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(cipherService.sm4Encrypt(user.getPhonenumber()));
        if (StringUtils.isNotNull(info) && info.getUserId() != userId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId() != userId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     * 
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     * 
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        if(StringUtils.isNotBlank(user.getUserName())) {
            log.info("新增系统用户 --> 待加密用户名：{}", user.getUserName());
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
            log.info("新增系统用户 --> 用户名密文：{}", user.getUserName());
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            log.info("新增系统用户 --> 待加密昵称：{}", user.getNickName());
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
            log.info("新增系统用户 --> 昵称密文：{}", user.getNickName());
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            log.info("新增系统用户 --> 待加密手机号：{}", user.getPhonenumber());
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
            log.info("新增系统用户 --> 手机号密文：{}", user.getPhonenumber());
        }

        if (StringUtils.isNotBlank(user.getPassword())) {
            log.info("新增系统用户 --> 待加密密码：{}", user.getPassword());
            user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
            log.info("新增系统用户 --> 密码密文：{}", user.getPassword());
        }

        user.setItemHash(cipherService.calcHash(user.joinItemHash()));
        log.info("新增系统用户 --> 用户名密文：{}, 昵称密文：{}, 手机号密文：{}, 密码密文：{}, 密文拼接字符串： {}, 密文拼接后计算的HASH: {}",
                user.getUserName(), user.getNickName(), user.getPhonenumber(),
                user.getPassword(), user.joinItemHash(), user.getItemHash()
        );

        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {

        if(StringUtils.isNotBlank(user.getUserName())) {
            log.info("注册系统用户 --> 待加密用户名：{}", user.getUserName());
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
            log.info("注册系统用户 --> 用户名密文：{}", user.getUserName());
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            log.info("注册系统用户 --> 待加密昵称：{}", user.getNickName());
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
            log.info("注册系统用户 --> 昵称密文：{}", user.getNickName());
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            log.info("注册系统用户 --> 待加密手机号：{}", user.getPhonenumber());
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
            log.info("注册系统用户 --> 手机号密文：{}", user.getPhonenumber());
        }

        if (StringUtils.isNotBlank(user.getPassword())) {
            log.info("注册系统用户 --> 待加密密码：{}", user.getPassword());
            user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
            log.info("注册系统用户 --> 密码密文：{}", user.getPassword());
        }

        user.setItemHash(cipherService.calcHash(user.joinItemHash()));
        log.info("注册系统用户 --> 用户名密文：{}, 昵称密文：{}, 手机号密文：{}, 密码密文：{}, 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", user.getUserName(), user.getNickName(), user.getPhonenumber(), user.getPassword(), user.joinItemHash(), user.getItemHash());

        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {

        if(StringUtils.isNotBlank(user.getUserName())) {
            log.info("修改系统用户 --> 待加密用户名：{}", user.getUserName());
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
            log.info("修改系统用户 --> 用户名密文：{}", user.getUserName());
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            log.info("修改系统用户 --> 待加密昵称：{}", user.getNickName());
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
            log.info("修改系统用户 --> 昵称密文：{}", user.getNickName());
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            log.info("修改系统用户 --> 待加密手机号：{}", user.getPhonenumber());
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
            log.info("修改系统用户 --> 手机号密文：{}", user.getPhonenumber());
        }

        user.setItemHash(cipherService.calcHash(user.joinItemHash()));
        log.info("修改系统用户 --> 用户名密文：{}, 昵称密文：{}, 密码密文：{}, 手机号密文：{}, 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", user.getUserName(), user.getNickName(), user.getPhonenumber(), user.getPassword(), user.joinItemHash(), user.getItemHash());

        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        if(StringUtils.isNotBlank(user.getUserName())) {
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
        }

        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        if(StringUtils.isNotBlank(user.getUserName())) {
            log.info("修改系统用户 --> 待加密用户名：{}", user.getUserName());
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
            log.info("修改系统用户 --> 用户名密文：{}", user.getUserName());
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            log.info("修改系统用户 --> 待加密昵称：{}", user.getNickName());
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
            log.info("修改系统用户 --> 昵称密文：{}", user.getNickName());
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            log.info("修改系统用户 --> 待加密手机号：{}", user.getPhonenumber());
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
            log.info("修改系统用户 --> 手机号密文：{}", user.getPhonenumber());
        }

        user.setItemHash(cipherService.calcHash(user.joinItemHash()));
        log.info("修改系统用户 --> 用户名密文：{}, 昵称密文：{}, 手机号密文：{}, 密码密文：{}, 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", user.getUserName(), user.getNickName(), user.getPhonenumber(), user.getPassword(), user.joinItemHash(), user.getItemHash());

        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(cipherService.sm4Encrypt(userName), avatar) > 0;
    }

    /**
     * 重置用户密码
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {

        if(StringUtils.isNotBlank(user.getUserName())) {
            log.info("重置系统用户密码 --> 待加密用户名：{}", user.getUserName());
            user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
            log.info("重置系统用户密码 --> 用户名密文：{}", user.getUserName());
        }

        if(StringUtils.isNotBlank(user.getNickName())) {
            log.info("重置系统用户密码 --> 待加密昵称：{}", user.getNickName());
            user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
            log.info("重置系统用户密码 --> 昵称密文：{}", user.getNickName());
        }

        if(StringUtils.isNotBlank(user.getPhonenumber())) {
            log.info("重置系统用户密码 --> 待加密手机号：{}", user.getPhonenumber());
            user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
            log.info("重置系统用户密码 --> 手机号密文：{}", user.getPhonenumber());
        }

        if (StringUtils.isNotBlank(user.getPassword())) {
            log.info("重置系统用户密码 --> 待加密密码：{}", user.getPassword());
            user.setPassword(cipherService.sm4Encrypt(user.getPassword()));
            log.info("重置系统用户密码 --> 密码密文：{}", user.getPassword());
        }

        user.setItemHash(cipherService.calcHash(user.joinItemHash()));
        log.info("重置系统用户密码 --> 用户名密文：{}, 昵称密文：{}, 手机号密文：{}, 密码密文：{}, 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", user.getUserName(), user.getNickName(), user.getPhonenumber(), user.getPassword(), user.joinItemHash(), user.getItemHash());

        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {

        String encryptedUserName = cipherService.sm4Encrypt(userName);
        String encryptedPassword = cipherService.sm4Encrypt(password);

        SysUser sysUser = userMapper.selectUserByUserName(encryptedUserName);
        if(sysUser == null) {
            throw new ServiceException("用户不存在");
        }

        sysUser.setPassword(encryptedPassword);
        sysUser.setItemHash(cipherService.calcHash(sysUser.joinItemHash()));
        sysUser.setUpdateTime(new Date());
        return userMapper.updateUser(sysUser);
    }

    /**
     * 新增用户角色信息
     * 
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     * 
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     * 
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>(roleIds.length);
            for (Long roleId : roleIds)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds)
        {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     * 
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList)) {
            throw new ServiceException("导入用户数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList)
        {
            try
            {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(cipherService.sm4Encrypt(user.getUserName()));
                if (StringUtils.isNull(u))
                {
                    BeanValidators.validateWithException(validator, user);
                    if(StringUtils.isNotBlank(user.getUserName())) {
                        user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
                    }

                    if(StringUtils.isNotBlank(user.getNickName())) {
                        user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
                    }

                    if(StringUtils.isNotBlank(user.getPhonenumber())) {
                        user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
                    }

                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setItemHash(cipherService.calcHash(user.joinItemHash()));
                    user.setCreateBy(operName);
                    this.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(user);
                    checkUserDataScope(user.getUserId());
                    user.setUpdateBy(operName);
                    user.setPassword(u.getPassword());

                    if(StringUtils.isNotBlank(user.getUserName())) {
                        user.setUserName(cipherService.sm4Encrypt(user.getUserName()));
                    }

                    if(StringUtils.isNotBlank(user.getNickName())) {
                        user.setNickName(cipherService.sm4Encrypt(user.getNickName()));
                    }

                    if(StringUtils.isNotBlank(user.getPhonenumber())) {
                        user.setPhonenumber(cipherService.sm4Encrypt(user.getPhonenumber()));
                    }

                    user.setItemHash(cipherService.calcHash(user.joinItemHash()));

                    this.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

}
