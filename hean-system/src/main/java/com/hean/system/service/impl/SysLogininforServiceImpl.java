package com.hean.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.hean.common.service.CipherService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.hean.system.domain.SysLogininfor;
import com.hean.system.mapper.SysLogininforMapper;
import com.hean.system.service.ISysLogininforService;

import javax.annotation.Resource;

/**
 * 系统访问日志情况信息 服务层处理
 */
@Service
@Slf4j
public class SysLogininforServiceImpl implements ISysLogininforService {

    @Resource
    private SysLogininforMapper logininforMapper;

    @Resource
    private CipherService cipherService;

    /**
     * 新增系统登录日志
     * 
     * @param logininfor 访问日志对象
     */
    @Override
    public void insertLogininfor(SysLogininfor logininfor) {
        if (StringUtils.isNotBlank(logininfor.getUserName())) {
            log.info("新增系统登录日志 --> 待加密用户名：{}", logininfor.getUserName());
            logininfor.setUserName(cipherService.sm4Encrypt(logininfor.getUserName()));
            log.info("新增系统登录日志 --> 用户名密文：{}", logininfor.getUserName());
        }

        if (StringUtils.isNotBlank(logininfor.getIpaddr())) {
            log.info("新增系统登录日志 --> 待加密IP地址：{}", logininfor.getIpaddr());
            logininfor.setIpaddr(cipherService.sm4Encrypt(logininfor.getIpaddr()));
            log.info("新增系统登录日志 --> IP地址密文：{}", logininfor.getIpaddr());
        }

        if (StringUtils.isNotBlank(logininfor.getMsg())) {
            log.info("新增系统登录日志 --> 待加密操作消息：{}", logininfor.getMsg());
            logininfor.setMsg(cipherService.sm4Encrypt(logininfor.getMsg()));
            log.info("新增系统登录日志 --> 操作消息密文：{}", logininfor.getMsg());
        }

        logininfor.setItemHash(cipherService.calcHash(logininfor.joinItemHash()));
        log.info("新增系统登录日志 --> 用户名密文：{}, IP地址密文：{}, 操作消息密文：{}, 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", logininfor.getUserName(), logininfor.getIpaddr(), logininfor.getMsg(), logininfor.joinItemHash(), logininfor.getItemHash());

        logininforMapper.insertLogininfor(logininfor);
    }

    /**
     * 查询系统登录日志集合
     * 
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor) {
        if (StringUtils.isNotBlank(logininfor.getUserName())) {
            logininfor.setUserName(cipherService.sm4Encrypt(logininfor.getUserName()));
        }

        if (StringUtils.isNotBlank(logininfor.getIpaddr())) {
            logininfor.setIpaddr(cipherService.sm4Encrypt(logininfor.getIpaddr()));
        }

        if (StringUtils.isNotBlank(logininfor.getMsg())) {
            logininfor.setMsg(cipherService.sm4Encrypt(logininfor.getMsg()));
        }

        return logininforMapper.selectLogininforList(logininfor).stream().peek(m -> {
            log.info("查询系统登录日志集合 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", m.joinItemHash(), m.getItemHash());

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            if(StringUtils.isNotBlank(m.getUserName())) {
                log.info("查询系统登录日志集合 --> 解密用户名密文：{}", m.getUserName());
                m.setUserName(cipherService.sm4Decrypt(m.getUserName()));
                log.info("查询系统登录日志集合 --> 解密后用户名明文：{}", m.getUserName());
            }

            if(StringUtils.isNotBlank(m.getIpaddr())) {
                log.info("查询系统登录日志集合 --> 解密IP地址密文：{}", m.getIpaddr());
                m.setIpaddr(cipherService.sm4Decrypt(m.getIpaddr()));
                log.info("查询系统登录日志集合 --> 解密后IP地址明文：{}", m.getIpaddr());
            }

            if(StringUtils.isNotBlank(m.getMsg())) {
                log.info("查询系统登录日志集合 --> 解密操作消息密文：{}", m.getMsg());
                m.setMsg(cipherService.sm4Decrypt(m.getMsg()));
                log.info("查询系统登录日志集合 --> 解密后操作消息明文：{}", m.getMsg());
            }
        }).collect(Collectors.toList());
    }

    /**
     * 批量删除系统登录日志
     * 
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return logininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor()
    {
        logininforMapper.cleanLogininfor();
    }
}
