package com.hean.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.hean.common.service.CipherService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.hean.system.domain.SysOperLog;
import com.hean.system.mapper.SysOperLogMapper;
import com.hean.system.service.ISysOperLogService;

import javax.annotation.Resource;

/**
 * 操作日志 服务层处理

 */
@Service
@Slf4j
public class SysOperLogServiceImpl implements ISysOperLogService {

    @Resource
    private SysOperLogMapper operLogMapper;

    @Resource
    private CipherService cipherService;

    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     */
    @Override
    public void insertOperlog(SysOperLog operLog) {
        if(StringUtils.isNotBlank(operLog.getOperName())) {
            log.info("新增操作日志 --> 待加密操作用户：{}", operLog.getOperName());
            operLog.setOperName(cipherService.sm4Encrypt(operLog.getOperName()));
            log.info("新增操作日志 --> 操作用户密文：{}", operLog.getOperName());
        }

        if(StringUtils.isNotBlank(operLog.getOperUrl())) {
            log.info("新增操作日志 --> 待加密操作地址：{}", operLog.getOperUrl());
            operLog.setOperUrl(cipherService.sm4Encrypt(operLog.getOperUrl()));
            log.info("新增操作日志 --> 操作地址密文：{}", operLog.getOperUrl());
        }

        if(StringUtils.isNotBlank(operLog.getOperParam())) {
            log.info("新增操作日志 --> 待加密操作参数：{}", operLog.getOperParam());
            operLog.setOperParam(cipherService.sm4Encrypt(operLog.getOperParam()));
            log.info("新增操作日志 --> 操作参数密文：{}", operLog.getOperParam());
        }

        if (StringUtils.isNotBlank(operLog.getJsonResult())) {
            log.info("新增操作日志 --> 待加密返回参数：{}", operLog.getJsonResult());
            operLog.setJsonResult(cipherService.sm4Encrypt(operLog.getJsonResult()));
            log.info("新增操作日志 --> 返回参数密文：{}", operLog.getJsonResult());
        }

        operLog.setItemHash(cipherService.calcHash(operLog.joinItemHash()));
        log.info("新增操作日志 --> 操作用户密文：{}, 操作地址密文：{}, 操作参数密文：{}, 返回参数密文：{}, 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", operLog.getOperName(), operLog.getOperUrl(), operLog.getOperParam(), operLog.getJsonResult(), operLog.joinItemHash(), operLog.getItemHash());

        operLogMapper.insertOperlog(operLog);
    }

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLog> selectOperLogList(SysOperLog operLog) {
        return operLogMapper.selectOperLogList(operLog).stream().peek(m -> {
            log.info("查询系统操作日志集合 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", m.joinItemHash(), m.getItemHash());

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            if(StringUtils.isNotBlank(m.getOperName())) {
                log.info("查询系统操作日志集合 --> 解密操作用户密文：{}", m.getOperName());
                m.setOperName(cipherService.sm4Decrypt(m.getOperName()));
                log.info("查询系统操作日志集合 --> 解密后操作用户明文：{}", m.getOperName());
            }

            if(StringUtils.isNotBlank(m.getOperUrl())) {
                log.info("查询系统操作日志集合 --> 解密操作地址密文：{}", m.getOperUrl());
                m.setOperUrl(cipherService.sm4Decrypt(m.getOperUrl()));
                log.info("查询系统操作日志集合 --> 解密后操作地址明文：{}", m.getOperUrl());
            }

            if(StringUtils.isNotBlank(m.getOperParam())) {
                log.info("查询系统操作日志集合 --> 解密操作参数密文：{}", m.getOperParam());
                m.setOperParam(cipherService.sm4Decrypt(m.getOperParam()));
                log.info("查询系统操作日志集合 --> 解密后操作参数明文：{}", m.getOperParam());
            }

            if(StringUtils.isNotBlank(m.getJsonResult())) {
                log.info("查询系统操作日志集合 --> 解密返回参数密文：{}", m.getJsonResult());
                m.setJsonResult(cipherService.sm4Decrypt(m.getJsonResult()));
                log.info("查询系统操作日志集合 --> 解密后返回参数明文：{}", m.getJsonResult());
            }
        }).collect(Collectors.toList());
    }

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(Long[] operIds) {
        return operLogMapper.deleteOperLogByIds(operIds);
    }

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public SysOperLog selectOperLogById(Long operId) {
        SysOperLog operLog = operLogMapper.selectOperLogById(operId);
        if(operLog == null) {
            return null;
        }

        log.info("查询操作日志详细 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", operLog.joinItemHash(), operLog.getItemHash());

        // 核验商密密文HASH
        cipherService.checkHash(operLog.joinItemHash(), operLog.getItemHash());

        if(StringUtils.isNotBlank(operLog.getOperName())) {
            log.info("查询操作日志详细 --> 解密操作用户密文：{}", operLog.getOperName());
            operLog.setOperName(cipherService.sm4Decrypt(operLog.getOperName()));
            log.info("查询操作日志详细 --> 解密后操作用户明文：{}", operLog.getOperName());
        }

        if(StringUtils.isNotBlank(operLog.getOperUrl())) {
            log.info("查询操作日志详细 --> 解密操作地址密文：{}", operLog.getOperUrl());
            operLog.setOperUrl(cipherService.sm4Decrypt(operLog.getOperUrl()));
            log.info("查询操作日志详细 --> 解密后操作地址明文：{}", operLog.getOperUrl());
        }

        if(StringUtils.isNotBlank(operLog.getOperParam())) {
            log.info("查询操作日志详细 --> 解密操作参数密文：{}", operLog.getOperParam());
            operLog.setOperParam(cipherService.sm4Decrypt(operLog.getOperParam()));
            log.info("查询操作日志详细 --> 解密后操作参数明文：{}", operLog.getOperParam());
        }

        if(StringUtils.isNotBlank(operLog.getJsonResult())) {
            log.info("查询操作日志详细 --> 解密返回参数密文：{}", operLog.getJsonResult());
            operLog.setJsonResult(cipherService.sm4Decrypt(operLog.getJsonResult()));
            log.info("查询操作日志详细 --> 解密后返回参数明文：{}", operLog.getJsonResult());
        }

        return operLog;
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog() {
        operLogMapper.cleanOperLog();
    }
}
