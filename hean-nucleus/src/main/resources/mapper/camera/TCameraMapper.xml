<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.camera.mapper.TCameraMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.camera.model.TCamera">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="recorder_bound_flag" property="recorderBoundFlag" />
        <result column="recorder_id" property="recorderId" />
        <result column="car_id" property="carId" />
        <result column="ys7_channel_no" property="ys7ChannelNo" />
        <result column="ys7_channel_name" property="ys7ChannelName" />
        <result column="hk_index_code" property="hkIndexCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_sys_user_id" property="deleteSysUserId" />
    </resultMap>

    <!-- 摄像头列表查询映射结果 -->
    <resultMap id="CameraListMap" type="com.hean.nucleus.domain.camera.response.CameraListResponse" extends="BaseResultMap">
        <result column="car_number" property="carNumber" />
        <result column="recorder_name" property="recorderName" />
        <result column="recorder_serial" property="recorderSerial" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 分页查询摄像头列表 -->
    <select id="list" resultMap="CameraListMap">
        select
            c.*,
            car.car_number,
            r.ys7_device_serial as recorder_serial,
            r.name as recorder_name,
            0 as status
        from t_camera as c
        left join t_car as car on c.car_id = car.id and car.delete_flag = 0
        left join t_recorder as r on c.recorder_id = r.id and r.delete_flag = 0
        where c.delete_flag = 0
        <if test="name != null and name != ''">
            and c.name like concat('%', #{name}, '%')
        </if>
        <if test="carId != null">
            and c.car_id = #{carId}
        </if>
        <if test="recorderId != null">
            and c.recorder_id = #{recorderId}
        </if>
        <if test="recorderBoundFlag != null">
            and c.recorder_bound_flag = #{recorderBoundFlag}
        </if>
        <if test="ys7ChannelNo != null">
            and c.ys7_channel_no = #{ys7ChannelNo}
        </if>
        <if test="ys7ChannelName != null and ys7ChannelName != ''">
            and c.ys7_channel_name like concat('%', #{ys7ChannelName}, '%')
        </if>
        <if test="hkIndexCode != null and hkIndexCode != ''">
            and c.hk_index_code like concat('%', #{hkIndexCode}, '%')
        </if>
        <if test="carNumber != null and carNumber != ''">
            and car.car_number like concat('%', #{carNumber}, '%')
        </if>
        order by c.create_time desc
    </select>

</mapper>
