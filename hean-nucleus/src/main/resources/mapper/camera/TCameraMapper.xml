<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.camera.mapper.TCameraMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.camera.model.TCamera">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="device_bound_flag" property="deviceBoundFlag" />
        <result column="device_id" property="deviceId" />
        <result column="car_id" property="carId" />
        <result column="ys7_channel_no" property="ys7ChannelNo" />
        <result column="ys7_channel_name" property="ys7ChannelName" />
        <result column="hk_index_code" property="hkIndexCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_sys_user_id" property="deleteSysUserId" />
    </resultMap>

</mapper>
