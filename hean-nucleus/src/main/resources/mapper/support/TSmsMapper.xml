<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.support.mapper.TSmsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.support.model.TSms">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="mobile" property="mobile" />
        <result column="message" property="message" />
        <result column="send_time" property="sendTime" />
        <result column="create_time" property="createTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
        <result column="item_hash" property="itemHash" />
    </resultMap>

    <!-- 分页查询短信记录列表 -->
    <select id="selectSmsRecordList" resultMap="BaseResultMap">
        SELECT
            id, user_id, mobile, message, send_time, create_time, delete_flag, delete_time, item_hash
        FROM
            t_sms
        WHERE delete_flag = 0
        <if test="request.userId != null">
            AND user_id = #{request.userId}
        </if>
        <if test="request.mobile != null and request.mobile != ''">
            AND mobile = #{request.mobile}
        </if>
        <if test="request.message != null and request.message != ''">
            AND message LIKE CONCAT('%', #{request.message}, '%')
        </if>
        <if test="request.startTime != null">
            AND send_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            AND send_time &lt;= #{request.endTime}
        </if>
        ORDER BY send_time DESC
    </select>

    <!-- 查询短信记录详情 -->
    <select id="selectSmsRecordDetail" resultMap="BaseResultMap">
        SELECT
            id, user_id, mobile, message, send_time, create_time, delete_flag, delete_time, item_hash
        FROM
            t_sms
        WHERE
            id = #{id}
            AND delete_flag = 0
    </select>

</mapper>
