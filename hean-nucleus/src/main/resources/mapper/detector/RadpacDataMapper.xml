<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.detector.mapper.RadpacDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.detector.model.RadpacData">
        <id column="id" property="id" />
        <result column="comm_id" property="commId" />
        <result column="monitor_sn" property="monitorSn" />
        <result column="gmttimestamp" property="gmttimestamp" />
        <result column="battery" property="battery" />
        <result column="gpsx" property="gpsx" />
        <result column="gpsy" property="gpsy" />
        <result column="temperature" property="temperature" />
        <result column="humidity" property="humidity" />
        <result column="hpa" property="hpa" />
        <result column="sensor_id" property="sensorId" />
        <result column="sensor_sn" property="sensorSn" />
        <result column="sensor_data" property="sensorData" />
        <result column="time_inserted" property="timeInserted" />
    </resultMap>

</mapper>
