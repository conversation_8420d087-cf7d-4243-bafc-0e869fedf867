<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.detector.mapper.RadpacInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.detector.model.RadpacInfo">
        <id column="id" property="radpacDetectorId" />
        <result column="comm_id" property="commId" />
        <result column="monitor_sn" property="monitorSn" />
        <result column="sensor_sn" property="sensorSn" />
        <result column="sensor_id" property="sensorId" />
        <result column="gmttimestamp" property="gmttimestamp" />
        <result column="sensor_data" property="sensorData" />
        <result column="data_unit" property="dataUnit" />
        <result column="sensor_status" property="sensorStatus" />
        <result column="monitor_ip" property="monitorIp" />
        <result column="monitor_port" property="monitorPort" />
        <result column="low_limit" property="lowLimit" />
        <result column="high_limit" property="highLimit" />
        <result column="duty_cycle" property="dutyCycle" />
    </resultMap>

    <select id="list" parameterType="com.hean.nucleus.domain.detector.request.RadpacDetectorListRequest" resultMap="BaseResultMap">
        select * from radpac_info
        <where>
            <if test="request.relatedFlag != null">
                <if test="request.relatedFlag == 0">
                    AND id NOT IN (SELECT radpac_detector_id FROM t_detector WHERE delete_flag = 0)
                </if>
                <if test="request.relatedFlag == 1">
                    AND id IN (SELECT radpac_detector_id FROM t_detector WHERE delete_flag = 0)
                </if>
            </if>
            <if test="request.radpacDetectorId != null and request.radpacDetectorId != ''">
                AND id = #{request.radpacDetectorId}
            </if>
        </where>
    </select>


</mapper>
