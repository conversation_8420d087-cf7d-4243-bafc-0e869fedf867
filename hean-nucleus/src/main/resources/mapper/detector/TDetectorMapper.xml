<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.detector.mapper.TDetectorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.detector.model.TDetector">
        <id column="id" property="id" />
        <result column="radpac_detector_id" property="radpacDetectorId" />
        <result column="name" property="name" />
        <result column="car_bound_flag" property="carBoundFlag" />
        <result column="car_id" property="carId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_sys_user_id" property="deleteSysUserId" />
    </resultMap>

    <resultMap id="DetectorListMap" type="com.hean.nucleus.domain.detector.bean.Detector" extends="BaseResultMap">
        <result column="car_bound_flag" property="carBoundFlag" />
        <result column="car_id" property="carId" />
        <result column="car_number" property="carNumber" />
    </resultMap>

    <!-- 查询探测器列表 -->
    <select id="list" parameterType="com.hean.nucleus.domain.detector.request.DetectorListRequest" resultMap="DetectorListMap">
        SELECT d.*, rs.*, c.car_number
        FROM t_detector d
        left join radpac_info rs on d.radpac_detector_id = rs.id and d.delete_flag = 0
        left join t_car c on c.id = d.car_id and c.delete_flag = 0
        WHERE 1=1
        <if test="request.name != null and request.name != ''">
            AND d.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="request.commId != null and request.commId != ''">
            AND rs.comm_id = #{request.commId}
        </if>
        <if test="request.monitorSn != null and request.monitorSn != ''">
            AND rs.monitor_sn = #{request.monitorSn}
        </if>
        <if test="request.sensorSn != null and request.sensorSn != ''">
            AND rs.sensor_sn = #{request.sensorSn}
        </if>
        <if test="request.sensorId != null and request.sensorId != ''">
            AND rs.sensor_id = #{request.sensorId}
        </if>
        <if test="request.carBoundFlag != null">
            AND d.car_bound_flag = #{request.carBoundFlag}
        </if>
        <if test="request.carId != null">
            AND d.car_id = #{request.carId}
        </if>
        <if test="request.carNumber != null and request.carNumber != ''">
            AND c.car_number LIKE CONCAT('%', #{request.carNumber}, '%')
        </if>
        ORDER BY d.create_time ASC
    </select>


</mapper>

