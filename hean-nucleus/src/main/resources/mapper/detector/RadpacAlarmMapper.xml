<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.RadpacAlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.recorder.model.RadpacAlarm">
        <id column="alarm_id" property="alarmId" />
        <result column="alarm_time" property="alarmTime" />
        <result column="device_id" property="deviceId" />
        <result column="alarm_type" property="alarmType" />
        <result column="alarm_action" property="alarmAction" />
        <result column="vidio_chan" property="vidioChan" />
        <result column="alarm_inchan" property="alarmInchan" />
        <result column="disk_num" property="diskNum" />
        <result column="remark" property="remark" />
        <result column="time_inserted" property="timeInserted" />
    </resultMap>

</mapper>
