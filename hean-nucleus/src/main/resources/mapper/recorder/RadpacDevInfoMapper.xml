<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.RadpacDevInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.recorder.model.RadpacDevInfo">
        <id column="device_id" property="radpacRecorderId" />
        <result column="dsk_num" property="dskNum" />
        <result column="dsk_size" property="dskSize" />
        <result column="dsk_remain" property="dskRemain" />
        <result column="defense_set" property="defenseSet" />
    </resultMap>

    <select id="list" parameterType="com.hean.nucleus.domain.recorder.request.RadpacRecorderListRequest" resultMap="BaseResultMap">
        select * from radpac_dev_info where 1=1
        <where>
            <if test="request.relatedFlag != null">
                <if test="request.relatedFlag == 0">
                    AND device_id NOT IN (SELECT radpac_recorder_id FROM t_recorder WHERE delete_flag = 0)
                </if>
                <if test="request.relatedFlag == 1">
                    AND device_id IN (SELECT radpac_recorder_id FROM t_recorder WHERE delete_flag = 0)
                </if>
            </if>
            <if test="request.radpacRecorderId != null and request.radpacRecorderId != ''">
                AND device_id LIKE CONCAT('%', #{request.radpacRecorderId}, '%')
            </if>
        </where>
    </select>


</mapper>
