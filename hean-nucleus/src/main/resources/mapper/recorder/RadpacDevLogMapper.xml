<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.RadpacDevLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.recorder.model.RadpacDevLog">
        <id column="id" property="id" />
        <result column="log_time" property="logTime" />
        <result column="major_type" property="majorType" />
        <result column="minor_type" property="minorType" />
        <result column="device_id" property="radpacRecorderId" />
    </resultMap>

    <resultMap id="LogListMap" type="com.hean.nucleus.domain.recorder.response.RecorderLogResponse" extends="BaseResultMap">
        <result column="car_id" property="carId" />
        <result column="car_number" property="carNumber" />
        <result column="description" property="description" />
    </resultMap>

    <!-- 分页查询设备日志列表 -->
    <select id="list" resultMap="LogListMap">
        SELECT 
            rdl.id,
            rdl.log_time,
            rdl.major_type,
            rdl.minor_type,
            rdl.device_id,
            tc.id as car_id,
            tc.car_number,
            rdld.describe as description
        FROM 
            radpac_dev_log rdl
        INNER JOIN
            t_recorder tr ON rdl.device_id = tr.radpac_recorder_id
        INNER JOIN
            t_car tc ON tr.car_id = tc.id AND tc.delete_flag = 0
        LEFT JOIN
            radpac_dev_log_detial rdld ON rdl.major_type = rdld.major_type AND rdl.minor_type = rdld.value
        WHERE
            tr.delete_flag = 0
            AND tr.car_bound_flag = 1
            <if test="carId != null">
                AND tc.id = #{carId}
            </if>
            <if test="majorType != null">
                AND rdl.major_type = #{majorType}
            </if>
            <if test="minorType != null and minorType != ''">
                AND rdl.minor_type = #{minorType}
            </if>
            <if test="startTime != null">
                AND rdl.log_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND rdl.log_time &lt;= #{endTime}
            </if>
        ORDER BY 
            rdl.log_time DESC, rdl.id DESC
    </select>

</mapper>
