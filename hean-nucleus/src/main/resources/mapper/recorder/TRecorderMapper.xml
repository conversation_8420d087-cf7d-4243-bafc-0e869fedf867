<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.TRecorderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.recorder.model.TRecorder">
        <id column="id" property="id" />
        <result column="radpac_recorder_id" property="radpacRecorderId" />
        <result column="ys7_device_serial" property="ys7DeviceSerial" />
        <result column="car_bound_flag" property="carBoundFlag" />
        <result column="car_id" property="carId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_sys_user_id" property="deleteSysUserId" />
    </resultMap>

    <resultMap id="RecorderListMap" type="com.hean.nucleus.domain.recorder.bean.Recorder" extends="BaseResultMap">
        <result column="ys7_device_serial" property="ys7DeviceSerial" />
        <result column="car_bound_flag" property="carBoundFlag" />
        <result column="car_id" property="carId" />
        <result column="car_number" property="carNumber" />
    </resultMap>

    <!-- 查询设备列表 -->
    <select id="list" parameterType="com.hean.nucleus.domain.recorder.request.RecorderListRequest" resultMap="RecorderListMap">
        SELECT r.*, rdi.*, c.car_number
        FROM t_recorder r
        left join radpac_dev_info rdi on r.radpac_recorder_id = rdi.device_id and r.delete_flag = 0
        left join t_car c on c.id = r.car_id and c.delete_flag = 0
        <where>
            <if test="request.radpacRecorderId != null and request.radpacRecorderId != ''">
                AND rdi.device_id LIKE CONCAT('%', #{request.radpacRecorderId}, '%')
            </if>
            <if test="request.diskNum != null">
                AND rdi.dsk_num = #{request.diskNum}
            </if>
            <if test="request.defenseSet != null">
                AND rdi.defense_set = #{request.defenseSet}
            </if>
            <if test="request.carBoundFlag != null">
                AND r.car_bound_flag = #{request.carBoundFlag}
            </if>
            <if test="request.carId != null">
                AND r.car_id = #{request.carId}
            </if>
            <if test="request.carNumber != null and request.carNumber != ''">
                AND c.car_number LIKE CONCAT('%', #{request.carNumber}, '%')
            </if>
        </where>
        ORDER BY rdi.device_id ASC
    </select>

</mapper>
