<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.RadpacDeviceMapper">

    <!-- 分页查询设备列表 -->
    <select id="selectDeviceList" parameterType="com.hean.nucleus.domain.recorder.request.RecorderListRequest" resultMap="com.hean.nucleus.domain.recorder.mapper.RadpacDevInfoMapper.BaseResultMap">
        SELECT
            device_id, dsk_num, dsk_size, dsk_remain
        FROM
            radpac_dev_info
        WHERE 1=1
        <if test="request.deviceId != null and request.deviceId != ''">
            AND device_id LIKE CONCAT('%', #{request.deviceId}, '%')
        </if>
        <if test="request.diskNum != null">
            AND dsk_num = #{request.diskNum}
        </if>
        ORDER BY device_id ASC
    </select>

    <!-- 查询设备详情 -->
    <select id="selectDeviceDetail" resultMap="com.hean.nucleus.domain.recorder.mapper.RadpacDevInfoMapper.BaseResultMap">
        SELECT
            device_id, dsk_num, dsk_size, dsk_remain
        FROM
            radpac_dev_info
        WHERE
            device_id = #{deviceId}
    </select>

    <!-- 分页查询录像计划列表 -->
    <select id="selectRecordPlanList" resultMap="com.hean.nucleus.domain.recorder.mapper.RadpacRecordPlanMapper.BaseResultMap">
        SELECT
            id, channel_num, week_num, is_use, record_time, pre_record_time, is_allday,
            record_type, start_time1, stop_time1, start_time2, stop_time2, down_load, device_id
        FROM
            radpac_record_plan
        WHERE 1=1
        <if test="request.deviceId != null and request.deviceId != ''">
            AND device_id = #{request.deviceId}
        </if>
        <if test="request.channelNum != null">
            AND channel_num = #{request.channelNum}
        </if>
        <if test="request.weekNum != null">
            AND week_num = #{request.weekNum}
        </if>
        ORDER BY device_id ASC, channel_num ASC, week_num ASC
    </select>

    <!-- 查询录像计划详情 -->
    <select id="selectRecordPlanDetail" resultMap="com.hean.nucleus.domain.recorder.mapper.RadpacRecordPlanMapper.BaseResultMap">
        SELECT
            id, channel_num, week_num, is_use, record_time, pre_record_time, is_allday,
            record_type, start_time1, stop_time1, start_time2, stop_time2, down_load, device_id
        FROM
            radpac_record_plan
        WHERE
            id = #{id}
    </select>

    <!-- 检查是否存在相同通道和星期的录像计划 -->
    <select id="checkDuplicateRecordPlan" resultMap="com.hean.nucleus.domain.recorder.mapper.RadpacRecordPlanMapper.BaseResultMap">
        SELECT
            id, channel_num, week_num, is_use, record_time, pre_record_time, is_allday,
            record_type, start_time1, stop_time1, start_time2, stop_time2, down_load, device_id
        FROM
            radpac_record_plan
        WHERE
            device_id = #{deviceId}
            AND channel_num = #{channelNum}
            AND week_num = #{weekNum}
        <if test="id != null">
            AND id != #{id}
        </if>
        LIMIT 1
    </select>

    <!-- 分页查询设备日志列表 -->
    <select id="selectDevLogList" resultMap="com.hean.nucleus.domain.recorder.mapper.RadpacDevLogMapper.BaseResultMap">
        SELECT
            id, log_time, major_type, minor_type, device_id
        FROM
            radpac_dev_log
        WHERE 1=1
        <if test="request.deviceId != null and request.deviceId != ''">
            AND device_id = #{request.deviceId}
        </if>
        <if test="request.majorType != null">
            AND major_type = #{request.majorType}
        </if>
        <if test="request.minorType != null and request.minorType != ''">
            AND minor_type = #{request.minorType}
        </if>
        <if test="request.startTime != null">
            AND log_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            AND log_time &lt;= #{request.endTime}
        </if>
        ORDER BY log_time DESC
    </select>

</mapper>
