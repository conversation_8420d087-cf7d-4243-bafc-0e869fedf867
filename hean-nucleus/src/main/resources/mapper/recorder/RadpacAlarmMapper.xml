<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.RadpacAlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.recorder.model.RadpacAlarm">
        <id column="alarm_id" property="alarmId" />
        <result column="alarm_time" property="alarmTime" />
        <result column="device_id" property="radpacRecorderId" />
        <result column="alarm_type" property="alarmType" />
        <result column="alarm_action" property="alarmAction" />
        <result column="vidio_chan" property="vidioChan" />
        <result column="alarm_inchan" property="alarmInchan" />
        <result column="disk_num" property="diskNum" />
        <result column="remark" property="remark" />
        <result column="time_inserted" property="timeInserted" />
    </resultMap>

    <resultMap id="AlarmListMap" type="com.hean.nucleus.domain.recorder.response.RecorderAlarmResponse" extends="BaseResultMap">
        <result column="car_id" property="carId" />
        <result column="car_number" property="carNumber" />
        <result column="process_flag" property="processFlag" />
        <result column="process_result" property="processResult" />
    </resultMap>

    <!-- 分页查询设备报警列表 -->
    <select id="list" resultMap="AlarmListMap">
        SELECT 
            ra.alarm_id,
            ra.alarm_time,
            ra.device_id,
            ra.alarm_type,
            ra.alarm_action,
            ra.vidio_chan,
            ra.alarm_inchan,
            ra.disk_num,
            ra.remark,
            ra.time_inserted,
            tc.id as car_id,
            tc.car_number,
            CASE WHEN tap.alarm_id IS NOT NULL THEN 1 ELSE 0 END as process_flag,
            tap.process_result
        FROM 
            radpac_alarm ra
        INNER JOIN
            t_recorder tr ON ra.device_id = tr.radpac_recorder_id
        INNER JOIN
            t_car tc ON tr.car_id = tc.id AND tc.delete_flag = 0
        LEFT JOIN
            t_alarm_process tap ON ra.alarm_id = tap.alarm_id AND tap.delete_flag = 0
        WHERE
            tr.delete_flag = 0
            AND tr.car_bound_flag = 1
            <if test="carId != null">
                AND tc.id = #{carId}
            </if>
            <if test="alarmType != null">
                AND ra.alarm_type = #{alarmType}
            </if>
            <if test="processFlag != null">
                <if test="processFlag == 1">
                    AND tap.alarm_id IS NOT NULL
                </if>
                <if test="processFlag == 0">
                    AND tap.alarm_id IS NULL
                </if>
            </if>
            <if test="startTime != null">
                AND ra.alarm_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ra.alarm_time &lt;= #{endTime}
            </if>
        ORDER BY 
            ra.alarm_time DESC, ra.alarm_id DESC
    </select>

</mapper>