<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.RadpacRecordPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.recorder.model.RadpacRecordPlan">
        <id column="id" property="id" />
        <result column="channel_num" property="channelNum" />
        <result column="week_num" property="weekNum" />
        <result column="is_use" property="isUse" />
        <result column="record_time" property="recordTime" />
        <result column="pre_record_time" property="preRecordTime" />
        <result column="is_allday" property="isAllday" />
        <result column="record_type" property="recordType" />
        <result column="start_time1" property="startTime1" />
        <result column="stop_time1" property="stopTime1" />
        <result column="start_time2" property="startTime2" />
        <result column="stop_time2" property="stopTime2" />
        <result column="down_load" property="downLoad" />
        <result column="device_id" property="deviceId" />
    </resultMap>


</mapper>
