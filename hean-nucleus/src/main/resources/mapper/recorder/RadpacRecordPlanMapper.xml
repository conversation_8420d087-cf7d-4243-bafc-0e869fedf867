<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.recorder.mapper.RadpacRecordPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.recorder.model.RadpacRecordPlan">
        <id column="id" property="id" />
        <result column="channel_num" property="channelNum" />
        <result column="week_num" property="weekNum" />
        <result column="is_use" property="isUse" />
        <result column="record_time" property="recordTime" />
        <result column="pre_record_time" property="preRecordTime" />
        <result column="is_allday" property="isAllday" />
        <result column="record_type" property="recordType" />
        <result column="start_time1" property="startTime1" />
        <result column="stop_time1" property="stopTime1" />
        <result column="start_time2" property="startTime2" />
        <result column="stop_time2" property="stopTime2" />
        <result column="down_load" property="downLoad" />
        <result column="device_id" property="radpacRecorderId" />
    </resultMap>

    <resultMap id="RecordPlanListMap" type="com.hean.nucleus.domain.recorder.response.RecordPlanResponse" extends="BaseResultMap">
        <result column="car_number" property="carNumber" />
    </resultMap>

    <!-- 检查是否存在相同通道和星期的录像计划 -->
    <select id="checkDuplicateRecordPlan" resultMap="BaseResultMap">
        SELECT
        id, channel_num, week_num, is_use, record_time, pre_record_time, is_allday,
        record_type, start_time1, stop_time1, start_time2, stop_time2, down_load, device_id
        FROM
        radpac_record_plan
        WHERE
        device_id = #{radpacRecorderId}
        AND channel_num = #{channelNum}
        AND week_num = #{weekNum}
        <if test="id != null">
            AND id != #{id}
        </if>
        LIMIT 1
    </select>

    <!-- 分页查询录像计划列表（关联车辆表） -->
    <select id="selectRecordPlanList" resultMap="RecordPlanListMap">
        SELECT 
            rrp.id,
            rrp.channel_num,
            rrp.week_num,
            rrp.is_use,
            rrp.record_time,
            rrp.pre_record_time,
            rrp.is_allday,
            rrp.record_type,
            rrp.start_time1,
            rrp.stop_time1,
            rrp.start_time2,
            rrp.stop_time2,
            rrp.down_load,
            rrp.device_id,
            tc.car_number
        FROM 
            radpac_record_plan rrp
        INNER JOIN
            t_recorder tr ON rrp.device_id = tr.radpac_recorder_id
        INNER JOIN
            t_car tc ON tr.car_id = tc.id AND tc.delete_flag = 0
        WHERE
            tr.delete_flag = 0
            AND tr.car_bound_flag = 1
            <if test="channelNum != null">
                AND rrp.channel_num = #{channelNum}
            </if>
            <if test="weekNum != null">
                AND rrp.week_num = #{weekNum}
            </if>
            <if test="carId != null">
                AND tc.id = #{carId}
            </if>
        ORDER BY 
            rrp.id DESC
    </select>

</mapper>
