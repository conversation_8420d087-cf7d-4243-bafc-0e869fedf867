<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.car.mapper.TCarGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.car.model.TCarGroup">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="name" property="name" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_sys_user_id" property="deleteSysUserId" />
    </resultMap>

    <resultMap id="CarGroupListMap" type="com.hean.nucleus.domain.car.response.CarGroupListResponse" extends="BaseResultMap">
        <result column="children_size" property="childrenSize" />
    </resultMap>

    <select id="list" resultMap="CarGroupListMap">
        select
        cg.*,
        (select count(*) from t_car_group where parent_id = cg.id and delete_flag = 0) as children_size
        from t_car_group as cg where cg.delete_flag = 0
        <if test="parentId != null">and cg.parent_id = #{parentId}</if>
        <if test="name != null and name != ''">and cg.name like concat('%', #{name}, '%')</if>
    </select>

</mapper>
