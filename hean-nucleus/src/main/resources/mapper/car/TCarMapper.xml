<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hean.nucleus.domain.car.mapper.TCarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hean.nucleus.domain.car.model.TCar">
        <id column="id" property="id" />
        <result column="car_group_id" property="carGroupId" />
        <result column="car_number" property="carNumber" />
        <result column="driver" property="driver" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_sys_user_id" property="deleteSysUserId" />
    </resultMap>

    <resultMap id="CarListMap" type="com.hean.nucleus.domain.car.response.CarListResponse" extends="BaseResultMap">
        <result column="car_group_name" property="carGroupName" />
    </resultMap>

    <select id="list" resultMap="CarListMap">
        select
        c.*, cg.name as car_group_name
        from t_car as c left join t_car_group as cg on c.car_group_id = cg.id
        where c.delete_flag = 0
        <if test="carNumber != null and carNumber != ''">and c.car_number = #{carNumber}</if>
        <if test="carGroupId != null">and c.car_group_id = #{carGroupId}</if>
    </select>

</mapper>
