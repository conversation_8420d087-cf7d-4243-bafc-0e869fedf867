package com.hean.nucleus.domain.recorder.response;

import com.hean.nucleus.domain.recorder.model.RadpacAlarm;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 海康NVR设备报警响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecorderAlarmResponse extends RadpacAlarm {

    /**
     * 车辆ID
     */
    private Long carId;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 是否已处置，0否，1是
     */
    private Integer processFlag;

    /**
     * 处置结果,processFlag=1时有值
     */
    private String processResult;

}


