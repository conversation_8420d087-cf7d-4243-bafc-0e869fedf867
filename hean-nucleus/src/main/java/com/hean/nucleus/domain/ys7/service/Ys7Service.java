package com.hean.nucleus.domain.ys7.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hean.common.core.redis.RedisCache;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.domain.ys7.bean.Ys7Channel;
import com.hean.nucleus.domain.ys7.bean.Ys7Page;
import com.hean.nucleus.domain.ys7.request.Ys7CameraPreviewUrlRequest;
import com.hean.nucleus.domain.ys7.request.Ys7ListPageRequest;
import com.hean.nucleus.domain.camera.response.PlayResponse;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class Ys7Service {

    @Value("${ys7.prefix}")
    private String prefix;

    @Value("${ys7.appKey}")
    private String appKey;

    @Value("${ys7.appSecret}")
    private String appSecret;

    @Resource
    private RedisCache redisCache;

    private final OkHttpClient httpClient = new OkHttpClient();

    private JSONObject post(String path, Map<String, Object> params) {
        FormBody.Builder builder = new FormBody.Builder();
        params.forEach((key, value) -> builder.add(key, value.toString()));
        RequestBody body = builder.build();
        Request request = new Request.Builder()
                .url(prefix + path)
                .post(body)
                .build();

        try(Response response = httpClient.newCall(request).execute()) {
            if(!response.isSuccessful()) {
                throw new ServiceException("萤石云接口请求失败");
            }

            if(response.body() == null) {
                throw new ServiceException("萤石云接口返回数据无效");
            }

            JSONObject resultJson = JSON.parseObject(response.body().string());
            if(resultJson.getInteger("code") != 200) {
                throw new ServiceException("萤石云接口请求失败: " + resultJson.getString("msg"));
            }

            return resultJson;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("请求失败", e);
        }
    }

    private synchronized String getAccessToken() {
        String accessToken = redisCache.getCacheObject("ys7:accessToken");
        if(accessToken != null) {
            return accessToken;
        }

        accessToken = post(
                "/lapp/token/get",
                Map.of("appKey", appKey, "appSecret", appSecret)
        ).getJSONObject("data").getString("accessToken");
        redisCache.setCacheObject("ys7:accessToken", accessToken, 6, TimeUnit.DAYS);
        return accessToken;
    }

    public Ys7Page<Ys7Channel> listCameraResources(Ys7ListPageRequest request) {
        JSONObject jsonObject = post(
                "/lapp/camera/list",
                Map.of(
                        "accessToken", getAccessToken(),
                        "pageStart", request.getPageNum() - 1,
                        "pageSize", request.getPageSize()
                )
        );

        JSONObject pageJson = jsonObject.getJSONObject("page");
        Ys7Page<Ys7Channel> page = new Ys7Page<>();
        page.setPageSize(pageJson.getInteger("size"));
        page.setPageNum(pageJson.getInteger("page"));
        page.setTotal(pageJson.getInteger("total"));
        page.setList(jsonObject.getJSONArray("data").toList(Ys7Channel.class));
        return page;
    }

    public List<Ys7Channel> getCameraResourceDetails(String deviceSerial) {
        JSONObject jsonObject = post(
                "/lapp/device/camera/list",
                Map.of(
                        "accessToken", getAccessToken(),
                        "deviceSerial", deviceSerial
                )
        );

        return jsonObject.getJSONArray("data").toList(Ys7Channel.class);
    }

    public PlayResponse getCameraUrl(String deviceSerial, Integer channelNo, Ys7CameraPreviewUrlRequest request) {

        Map<String, Object> params = new HashMap<>();
        params.put("accessToken", getAccessToken());
        params.put("deviceSerial", deviceSerial);
        params.put("channelNo", channelNo);
        if(request.getProtocol() != null) {
            params.put("protocol", request.getProtocol());
        }
        if(request.getExpireTime() != null) {
            params.put("expireTime", request.getExpireTime());
        }
        if(StringUtils.isNotBlank(request.getType())) {
            params.put("type", request.getType());
        }
        if(request.getQuality() != null) {
            params.put("quality", request.getQuality());
        }
        if(StringUtils.isNotBlank(request.getStartTime())) {
            params.put("startTime", request.getStartTime());
        }
        if(StringUtils.isNotBlank(request.getStopTime())) {
            params.put("endTime", request.getStopTime());
        }
        if(request.getSupportH265() != null) {
            params.put("supportH265", request.getSupportH265());
        }

        JSONObject jsonObject = post("/lapp/v2/live/address/get", params);
        PlayResponse response = new PlayResponse();
        response.setUrl(jsonObject.getJSONObject("data").getString("url"));
        response.setAccessToken(getAccessToken());
        return response;
    }

}
