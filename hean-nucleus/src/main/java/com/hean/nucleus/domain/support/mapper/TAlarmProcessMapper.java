package com.hean.nucleus.domain.support.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.support.model.TAlarmProcess;
import com.hean.nucleus.domain.recorder.request.AlarmProcessListRequest;
import com.hean.nucleus.domain.recorder.response.AlarmProcessResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 告警处置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface TAlarmProcessMapper extends CoreMapper<TAlarmProcess> {

    /**
     * 分页查询告警处置列表
     *
     * @param request 查询条件
     * @return 告警处置列表
     */
    List<AlarmProcessResponse> selectAlarmProcessList(@Param("request") AlarmProcessListRequest request);

    /**
     * 查询告警处置详情
     *
     * @param alarmId 告警ID
     * @return 告警处置详情
     */
    AlarmProcessResponse selectAlarmProcessDetail(@Param("alarmId") Long alarmId);
}
