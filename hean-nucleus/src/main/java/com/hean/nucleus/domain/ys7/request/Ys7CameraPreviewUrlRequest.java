package com.hean.nucleus.domain.ys7.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class Ys7CameraPreviewUrlRequest {

    @NotNull(message = "摄像头ID不能为空")
    private Long cameraId;

    //流播放协议，1-ezopen、2-hls、3-rtmp、4-flv，默认为1
    private Integer protocol;

    //过期时长，单位秒；针对hls/rtmp/flv设置有效期，相对时间；30秒-720天
    private Integer expireTime;

    //地址的类型，1-预览，2-本地录像回放，3-云存储录像回放，非必选，默认为1；回放仅支持rtmp、ezopen、flv协议
    private String type;

    //视频清晰度，1-高清（主码流）、2-流畅（子码流）
    private Integer quality;

    //本地录像/云存储录像回放开始时间,云存储开始结束时间必须在同一天，示例：2019-12-01 00:00:00
    private String startTime;

    //本地录像/云存储录像回放结束时间,云存储开始结束时间必须在同一天，示例：2019-12-01 23:59:59
    private String stopTime;

    //请判断播放端是否要求播放视频为H265编码格式,1表示需要，0表示不要求
    private Integer supportH265;
}
