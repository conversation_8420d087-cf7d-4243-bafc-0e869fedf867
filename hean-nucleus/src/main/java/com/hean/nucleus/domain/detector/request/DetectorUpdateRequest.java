package com.hean.nucleus.domain.detector.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 探测器添加请求
 */
@Data
public class DetectorUpdateRequest {

    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * radpac表主键id
     */
    @NotNull(message = "探测器id不能为空")
    private Long radpacDetectorId;

    @NotNull(message = "探测器名称不能为空")
    private String name;

}
