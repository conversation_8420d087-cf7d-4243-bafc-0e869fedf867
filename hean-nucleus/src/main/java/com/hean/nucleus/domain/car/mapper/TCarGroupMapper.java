package com.hean.nucleus.domain.car.mapper;

import com.hean.nucleus.common.database.CoreMapper;
import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.domain.car.model.TCarGroup;
import com.hean.nucleus.domain.car.request.CarGroupListRequest;
import com.hean.nucleus.domain.car.response.CarGroupListResponse;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface TCarGroupMapper extends CoreMapper<TCarGroup> {

    List<CarGroupListResponse> list(CarGroupListRequest request);

}
