package com.hean.nucleus.domain.recorder.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class AlarmLogRequest {

    //事件规则id
    private String eventRuleId;

    //事件分类，详见[附录A.62 事件分类]@[软件产品-综合安防管理平台-附录-附录A 数据字典#附录A.62 事件分类]
    private String ability;

    //区域编号
    private String regionIndexCode;

    //所属位置，详见[附录A.81 安装位置]@[软件产品-综合安防管理平台-附录-附录A 数据字典#附录A.81 安装位置]
    private List<String> locationIndexCodes;

    //事件源名称
    private String resName;

    //事件源编号
    private List<String> resIndexCodes;

    //事件源类型，详见[附录A.63 事件源类型]@[软件产品-综合安防管理平台-附录-附录A 数据字典#附录A.63 事件源类型]
    private List<String> resTypes;

    //事件类型，参考[附录D]@[软件产品-综合安防管理平台-附录-附录D 事件列表]
    private String eventType;

    //事件等级，1-低，2-中，3-高
    private List<String> eventLevels;

    //处理状态，0-未处理，1-已处理
    private Integer handleStatus;

    //开始时间
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    //结束时间
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    //分页大小
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize;

    //页码
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

}
