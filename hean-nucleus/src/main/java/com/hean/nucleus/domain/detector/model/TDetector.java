package com.hean.nucleus.domain.detector.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * <p>
 * 探测器
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_detector")
public class TDetector {

    /**
     * 探测器ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * RADPAC_INFO表主键ID
     */
    private Long radpacDetectorId;

    /**
     * 是否已绑定车辆，0:未绑定，1:已绑定
     */
    private Integer carBoundFlag;

    /**
     * 车辆ID
     */
    private Long carId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记：1=已删除，0=未删除
     */
    private Integer deleteFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

    /**
     * 后台删除操作用户
     */
    private Long deleteSysUserId;


}
