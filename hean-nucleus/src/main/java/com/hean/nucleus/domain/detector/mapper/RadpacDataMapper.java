package com.hean.nucleus.domain.detector.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.detector.model.RadpacData;

/**
 * <p>
 * 探测数据记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacDataMapper extends CoreMapper<RadpacData> {

}
