package com.hean.nucleus.domain.recorder.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 告警处置添加请求
 */
@Data
public class AlarmProcessAddRequest {

    /**
     * 告警ID
     */
    @NotNull(message = "告警ID不能为空")
    private Long alarmId;

    /**
     * 处置结果
     */
    @NotBlank(message = "处置结果不能为空")
    private String processResult;

    /**
     * 处置时间
     */
    @NotNull(message = "处置时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processTime;
}
