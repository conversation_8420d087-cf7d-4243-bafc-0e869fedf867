package com.hean.nucleus.domain.detector.mapper;

import com.hean.nucleus.domain.detector.bean.Detector;
import com.hean.nucleus.domain.detector.model.TDetector;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.domain.detector.request.DetectorListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 探头 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface TDetectorMapper extends CoreMapper<TDetector> {

    /**
     * 查询探测器列表
     *
     * @param request 查询条件
     * @return 设备列表
     */
    List<Detector> list(@Param("request") DetectorListRequest request);

}
