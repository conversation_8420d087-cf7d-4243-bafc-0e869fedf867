package com.hean.nucleus.domain.recorder.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * radpac海康NVR设备表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("radpac_dev_info")
public class RadpacDevInfo {

    /**
     * 设备ID
     */
    @TableId(value = "device_id")
    private String radpacRecorderId;

    /**
     * 硬盘编号
     */
    private Integer dskNum;

    /**
     * 硬盘大小，单位MB
     */
    private Integer dskSize;


    /**
     * 硬盘剩余容量，单位MB
     */
    private Integer dskRemain;

    /**
     * 布防设置，0：撤防、1：布防
     */
    private Integer defenseSet;

}
