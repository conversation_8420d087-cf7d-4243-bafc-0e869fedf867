package com.hean.nucleus.domain.car.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.annotation.MethodDoc;
import com.hean.common.core.domain.model.LoginUser;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.domain.car.mapper.TCarGroupMapper;
import com.hean.nucleus.domain.car.model.TCarGroup;
import com.hean.nucleus.domain.car.request.CarGroupListRequest;
import com.hean.nucleus.domain.car.request.CarGroupSaveRequest;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.response.CarGroupListResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


@Slf4j
@Service
public class CarGroupService {

    @Resource
    private TCarGroupMapper carGroupMapper;

    //添加车辆分组
    public void add(CarGroupSaveRequest request) {
        if (request.getParentId() != 0) {
            TCarGroup parent = carGroupMapper.selectById(request.getParentId());
            if (parent == null) {
                throw new ServiceException("未找到父级分组");
            }
        }

        TCarGroup carGroup = new TCarGroup();
        BeanUtils.copyProperties(request, carGroup);
        carGroup.setDeleteFlag(0);
        carGroup.setCreateTime(LocalDateTime.now());
        carGroup.setUpdateTime(LocalDateTime.now());
        carGroupMapper.insert(carGroup);
    }

    //删除车辆分组
    public void delete(LoginUser loginUser, Long id) {
        TCarGroup carGroup = carGroupMapper.selectById(id);
        if (carGroup == null) {
            throw new ServiceException("未找到分组记录");
        }
        carGroup.setDeleteFlag(1);
        carGroup.setDeleteTime(LocalDateTime.now());
        carGroup.setDeleteSysUserId(loginUser.getUserId());
        carGroupMapper.updateById(carGroup);
    }

    //修改车辆分组
    public void update(CarGroupSaveRequest request) {
        TCarGroup carGroup = carGroupMapper.selectById(request.getId());
        if (carGroup == null) {
            throw new ServiceException("未找到分组记录");
        }
        if (request.getParentId() != 0) {
            TCarGroup parent = carGroupMapper.selectById(request.getParentId());
            if (parent == null) {
                throw new ServiceException("未找到父级分组");
            }
        }
        carGroup.setParentId(request.getParentId());
        carGroup.setName(request.getName());
        carGroup.setUpdateTime(LocalDateTime.now());
        carGroupMapper.updateById(carGroup);
    }

    //查询车辆分组详情
    public TCarGroup detail(IdRequest request) {
        TCarGroup carGroup = carGroupMapper.selectById(request.getId());
        if (carGroup == null) {
            throw new ServiceException("未找到分组记录");
        }
        return carGroup;
    }

    public List<CarGroupListResponse> list(CarGroupListRequest request) {
        return carGroupMapper.list(request);
    }

    //查询车辆分组分页列表
    @MethodDoc(name = "广东省城市放射源收贮车辆分组信息")
    public PageView<CarGroupListResponse> listPage(CarGroupListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        PageInfo<CarGroupListResponse> pageInfo = new PageInfo<>(carGroupMapper.list(request));
        return PageView.from(pageInfo, pageInfo.getList());
    }

}
