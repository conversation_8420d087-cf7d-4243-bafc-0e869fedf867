package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.bean.Recorder;
import com.hean.nucleus.domain.recorder.model.TRecorder;
import com.hean.nucleus.domain.recorder.request.RecorderListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 海康NVR设备 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface TRecorderMapper extends CoreMapper<TRecorder> {


    /**
     * 查询设备列表
     *
     * @param request 查询条件
     * @return 设备列表
     */
    List<Recorder> list(@Param("request") RecorderListRequest request);

}
