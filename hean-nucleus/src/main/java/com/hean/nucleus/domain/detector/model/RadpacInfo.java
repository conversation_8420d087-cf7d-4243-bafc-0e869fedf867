package com.hean.nucleus.domain.detector.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * radpac探测器设备信息表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("radpac_info")
public class RadpacInfo {

    /**
     * 索引
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long radpacDetectorId;

    /**
     * 主机通讯编号
     */
    private String commId;

    /**
     * 主机序列号
     */
    private String monitorSn;

    /**
     * 探测器序列号
     */
    private String sensorSn;

    /**
     * 探测器ID
     */
    private String sensorId;

    /**
     * 主机时间
     */
    private LocalDateTime gmttimestamp;

    /**
     * 探测器数据
     */
    private Double sensorData;

    /**
     * 数据单位
     */
    private String dataUnit;

    /**
     * 设备状态
     */
    private Integer sensorStatus;

    /**
     * 设备IP
     */
    private String monitorIp;

    /**
     * 设备端口
     */
    private Integer monitorPort;

    /**
     * 低阈值
     */
    private Double lowLimit;

    /**
     * 高阈值
     */
    private Double highLimit;

    /**
     * 数据上传周期
     */
    private Integer dutyCycle;


}
