package com.hean.nucleus.domain.support.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.exception.ServiceException;
import com.hean.common.service.CipherService;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.domain.support.mapper.TSmsMapper;
import com.hean.nucleus.domain.support.model.TSms;
import com.hean.nucleus.domain.support.request.SmsAddRequest;
import com.hean.nucleus.domain.support.request.SmsListRequest;
import com.hean.nucleus.domain.support.response.SmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信服务
 */
@Slf4j
@Service
public class SmsService {

    @Resource
    private TSmsMapper smsMapper;

    @Resource
    private CipherService cipherService;

    /**
     * 添加短信记录
     *
     * @param request 短信信息
     */
    public void addSmsRecord(SmsAddRequest request) {
        TSms sms = new TSms();
        BeanUtils.copyProperties(request, sms);

        sms.setMobile(cipherService.sm4Encrypt(sms.getMobile()));
        sms.setMessage(cipherService.sm4Encrypt(sms.getMessage()));
        sms.setItemHash(cipherService.calcHash(sms.joinItemHash()));

        sms.setSendTime(LocalDateTime.now());
        sms.setCreateTime(LocalDateTime.now());
        sms.setDeleteFlag(0);

        smsMapper.insert(sms);
    }

    /**
     * 删除短信记录
     *
     * @param id 短信记录ID
     */
    public void deleteSmsRecord(Long id) {
        // 检查短信记录是否存在
        TSms existSms = smsMapper.selectSmsRecordDetail(id);
        if (existSms == null) {
            throw new ServiceException("短信记录不存在");
        }

        // 软删除
        existSms.setDeleteFlag(1);
        existSms.setDeleteTime(LocalDateTime.now());
        smsMapper.updateById(existSms);
    }

    /**
     * 短信分页查询列表
     *
     * @param request 查询条件
     * @return 短信记录列表
     */
    public PageView<SmsResponse> listSmsPage(SmsListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        // 使用mapper的SQL查询方法
        List<TSms> list = smsMapper.selectSmsRecordList(request);
        PageInfo<TSms> pageInfo = new PageInfo<>(list);

        // 转换为响应对象
        List<SmsResponse> responseList = list.stream().map(item -> {
            // 核验商密密文HASH
            cipherService.checkHash(item.joinItemHash(), item.getItemHash());

            SmsResponse response = new SmsResponse();
            BeanUtils.copyProperties(item, response);
            response.setMobile(cipherService.sm4Decrypt(response.getMobile()));
            response.setMessage(cipherService.sm4Decrypt(response.getMessage()));
            return response;
        }).collect(Collectors.toList());

        return PageView.from(pageInfo, responseList);
    }

    /**
     * 查询短信记录详情
     *
     * @param id 短信记录ID
     * @return 短信记录详情
     */
    public SmsResponse getSmsRecordDetail(Long id) {
        TSms sms = smsMapper.selectSmsRecordDetail(id);
        if (sms == null) {
            throw new ServiceException("短信记录不存在");
        }

        SmsResponse response = new SmsResponse();
        BeanUtils.copyProperties(sms, response);
        // 核验商密密文HASH
        cipherService.checkHash(sms.joinItemHash(), sms.getItemHash());

        response.setMobile(cipherService.sm4Decrypt(response.getMobile()));
        response.setMessage(cipherService.sm4Decrypt(response.getMessage()));
        return response;
    }
}
