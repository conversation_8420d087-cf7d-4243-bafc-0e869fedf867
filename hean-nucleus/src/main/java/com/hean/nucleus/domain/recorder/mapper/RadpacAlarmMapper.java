package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.model.RadpacAlarm;
import com.hean.nucleus.domain.recorder.request.RecorderAlarmListRequest;
import com.hean.nucleus.domain.recorder.response.RecorderAlarmResponse;

import java.util.List;

/**
 * <p>
 * 海康设备报警 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacAlarmMapper extends CoreMapper<RadpacAlarm> {

    /**
     * 分页查询设备报警列表
     *
     * @param request 查询条件
     * @return 设备报警列表
     */
    List<RecorderAlarmResponse> list(RecorderAlarmListRequest request);

}
