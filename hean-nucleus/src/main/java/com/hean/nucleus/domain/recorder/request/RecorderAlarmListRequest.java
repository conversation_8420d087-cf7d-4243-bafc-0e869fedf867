package com.hean.nucleus.domain.recorder.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class RecorderAlarmListRequest {

    /** 当前记录起始索引 */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    /** 每页显示记录数 */
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize;

    private Long carId;

    private Integer alarmType;

    /**
     * 是否已处置，0否，1是
     */
    private Integer processFlag;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

}
