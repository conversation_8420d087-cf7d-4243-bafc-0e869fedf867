package com.hean.nucleus.domain.recorder.request;

import com.hean.common.annotation.FieldDoc;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * 海康NVR设备查询请求
 */
@Data
public class RecorderListRequest {

    /**
     * 当前记录起始索引
     */
    @FieldDoc(friendName = "页码", required = true, description = "分页页码")
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    @FieldDoc(friendName = "分页大小", required = true, description = "分页大小")
    private Integer pageSize;

    /**
     * radpac设备ID
     */
    @FieldDoc(friendName = "设备ID", required = false, description = "设备ID")
    private String radpacRecorderId;

    /**
     * 硬盘编号
     */
    @FieldDoc(friendName = "硬盘编号", required = false, description = "硬盘编号")
    private Integer diskNum;

    /**
     * 布防状态，0-撤防，1-布防
     */
    @FieldDoc(friendName = "布防状态", required = false, description = "布防状态，0-撤防，1-布防")
    @Range(min = 0, max = 1, message = "布防状态无效")
    private Integer defenseSet;

    /**
     * 是否已绑定车辆，0:未绑定，1:已绑定
     */
    @FieldDoc(friendName = "绑定状态", required = false, description = "是否已绑定车辆，0:未绑定，1:已绑定")
    @Range(min = 0, max = 1, message = "绑定状态无效")
    private Integer carBoundFlag;

    @FieldDoc(friendName = "车辆ID", required = false, description = "车辆ID")
    private Long carId;

    /**
     * 车牌号
     */
    @FieldDoc(friendName = "车牌号", required = false, description = "车牌号")
    private String carNumber;

}
