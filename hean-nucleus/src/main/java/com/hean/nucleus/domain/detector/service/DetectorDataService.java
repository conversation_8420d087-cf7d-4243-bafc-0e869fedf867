package com.hean.nucleus.domain.detector.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.domain.detector.mapper.RadpacDataMapper;
import com.hean.nucleus.domain.detector.mapper.RadpacInfoMapper;
import com.hean.nucleus.domain.detector.mapper.TDetectorMapper;
import com.hean.nucleus.domain.detector.model.RadpacData;
import com.hean.nucleus.domain.detector.model.RadpacInfo;
import com.hean.nucleus.domain.detector.model.TDetector;
import com.hean.nucleus.domain.detector.request.DetectorDataListRequest;
import com.hean.nucleus.domain.detector.request.DetectorDataTrailRequest;
import com.hean.nucleus.common.request.IdRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DetectorDataService {

    @Resource
    private RadpacInfoMapper radpacInfoMapper;

    @Resource
    private RadpacDataMapper radpacDataMapper;

    @Resource
    private TDetectorMapper detectorMapper;

    /**
     * 根据radpac探测器ID获取最新数据
     * @param request
     * @return
     */
    public RadpacData lastData(IdRequest request) {
        // 判断探测器是否存在
        TDetector detector = detectorMapper.selectById(request.getId());
        if(detector == null || detector.getDeleteFlag() == 1) {
            throw new ServiceException("探测器不存在");
        }

        RadpacInfo radpacInfo = radpacInfoMapper.selectById(detector.getRadpacDetectorId());
        if (radpacInfo == null) {
            throw new ServiceException("radpac探测器不存在");
        }

        RadpacData data = radpacDataMapper.selectFirst(s -> s
                .eq(RadpacData::getSensorId, radpacInfo.getSensorId())
                .isNotNull(RadpacData::getGpsx)
                .isNotNull(RadpacData::getGpsy)
                .orderByDesc(RadpacData::getGmttimestamp)
        );

        if(data == null) {
            throw new ServiceException("探头无数据");
        }

        // sensorData 保留两位小数，四舍五入
        data.setSensorData(Double.parseDouble(String.format("%.2f", data.getSensorData())));

        return data;
    }

    public List<RadpacData> trailData(DetectorDataTrailRequest request) {
        // 判断探测器是否存在
        TDetector detector = detectorMapper.selectById(request.getId());
        if(detector == null || detector.getDeleteFlag() == 1) {
            throw new ServiceException("探测器不存在");
        }

        RadpacInfo radpacInfo = radpacInfoMapper.selectById(detector.getRadpacDetectorId());
        if (radpacInfo == null) {
            throw new ServiceException("探测器不存在");
        }

        LambdaQueryWrapper<RadpacData> query = new LambdaQueryWrapper<RadpacData>()
                .eq(RadpacData::getSensorId, radpacInfo.getSensorId())
                .isNotNull(RadpacData::getGpsx)
                .isNotNull(RadpacData::getGpsy);
        if(request.getStartTime() != null) {
            query.ge(RadpacData::getGmttimestamp, request.getStartTime());
        }
        if(request.getEndTime() != null) {
            query.le(RadpacData::getGmttimestamp, request.getEndTime());
        }
        query.orderByDesc(RadpacData::getGmttimestamp);

        return radpacDataMapper.selectList(query);
    }

    public PageView<RadpacData> listDataPage(DetectorDataListRequest request) {
        if(request.getId() != null) {
            TDetector detector = detectorMapper.selectById(request.getId());
            if(detector == null || detector.getDeleteFlag() == 1) {
                throw new ServiceException("探测器不存在");
            }

            request.setRadpacDetectorId(detector.getRadpacDetectorId());
        }

        if(request.getRadpacDetectorId() != null) {
            RadpacInfo radpacInfo = radpacInfoMapper.selectById(request.getRadpacDetectorId());
            if (radpacInfo == null) {
                throw new ServiceException("radpac探测器不存在");
            }

            request.setSensorId(radpacInfo.getSensorId());
        }

        LambdaQueryWrapper<RadpacData> query = new LambdaQueryWrapper<RadpacData>()
                .isNotNull(RadpacData::getGpsx)
                .isNotNull(RadpacData::getGpsy);

        if(StringUtils.isNotBlank(request.getCommId())) {
            query.eq(RadpacData::getCommId, request.getCommId());
        }
        if(StringUtils.isNotBlank(request.getMonitorSn())) {
            query.eq(RadpacData::getMonitorSn, request.getMonitorSn());
        }
        if(StringUtils.isNotBlank(request.getSensorId())) {
            query.eq(RadpacData::getSensorId, request.getSensorId());
        }
        if(StringUtils.isNotBlank(request.getSensorSn())) {
            query.eq(RadpacData::getSensorSn, request.getSensorSn());
        }
        if(request.getStartTime() != null) {
            query.ge(RadpacData::getGmttimestamp, request.getStartTime());
        }
        if(request.getEndTime() != null) {
            query.le(RadpacData::getGmttimestamp, request.getEndTime());
        }
        query.orderByDesc(RadpacData::getId);

        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        PageInfo<RadpacData> pageInfo = new PageInfo<>(radpacDataMapper.selectList(query));
        return PageView.from(pageInfo, pageInfo.getList());
    }

    public List<RadpacData> listData(DetectorDataListRequest request) {
        if(request.getId() != null) {
            TDetector detector = detectorMapper.selectById(request.getId());
            if(detector == null || detector.getDeleteFlag() == 1) {
                throw new ServiceException("探测器不存在");
            }

            request.setRadpacDetectorId(detector.getRadpacDetectorId());
        }

        if(request.getRadpacDetectorId() != null) {
            RadpacInfo radpacInfo = radpacInfoMapper.selectById(request.getRadpacDetectorId());
            if (radpacInfo == null) {
                throw new ServiceException("探测器不存在");
            }

            request.setSensorId(radpacInfo.getSensorId());
        }

        LambdaQueryWrapper<RadpacData> query = new LambdaQueryWrapper<RadpacData>()
                .isNotNull(RadpacData::getGpsx)
                .isNotNull(RadpacData::getGpsy);

        if(StringUtils.isNotBlank(request.getCommId())) {
            query.eq(RadpacData::getCommId, request.getCommId());
        }
        if(StringUtils.isNotBlank(request.getMonitorSn())) {
            query.eq(RadpacData::getMonitorSn, request.getMonitorSn());
        }
        if(StringUtils.isNotBlank(request.getSensorId())) {
            query.eq(RadpacData::getSensorId, request.getSensorId());
        }
        if(StringUtils.isNotBlank(request.getSensorSn())) {
            query.eq(RadpacData::getSensorSn, request.getSensorSn());
        }
        if(request.getStartTime() != null) {
            query.ge(RadpacData::getGmttimestamp, request.getStartTime());
        }
        if(request.getEndTime() != null) {
            query.le(RadpacData::getGmttimestamp, request.getEndTime());
        }
        query.orderByDesc(RadpacData::getId);

        return radpacDataMapper.selectList(query);
    }

}
