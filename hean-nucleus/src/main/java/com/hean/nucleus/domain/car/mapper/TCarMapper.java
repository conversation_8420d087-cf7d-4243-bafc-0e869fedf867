package com.hean.nucleus.domain.car.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.car.request.CarListRequest;
import com.hean.nucleus.domain.car.response.CarListResponse;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface TCarMapper extends CoreMapper<TCar> {

    List<CarListResponse> list(CarListRequest request);

}
