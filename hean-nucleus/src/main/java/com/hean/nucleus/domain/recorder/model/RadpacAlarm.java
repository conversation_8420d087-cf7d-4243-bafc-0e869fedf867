package com.hean.nucleus.domain.recorder.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * radpac海康NVR设备报警记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("radpac_alarm")
public class RadpacAlarm {

    /**
     * 报警记录ID
     */
    @TableId(value = "alarm_id", type = IdType.AUTO)
    private Long alarmId;

    /**
     * 报警触发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime alarmTime;

    /**
     * radpac_dev_info表关联设备ID
     */
    private String deviceId;

    /**
     * 报警类型
     */
    private Integer alarmType;

    /**
     * 报警动作
     */
    private Integer alarmAction;

    /**
     * 视频通道
     */
    private Integer vidioChan;

    /**
     * IO输入通道
     */
    private Integer alarmInchan;

    /**
     * 硬盘编号
     */
    private Integer diskNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 服务器数据插入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime timeInserted;


}
