package com.hean.nucleus.domain.detector.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * radpac探测器探测数据记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("radpac_data")
public class RadpacData {

    /**
     * 数据记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主机通讯编号
     */
    private String commId;

    /**
     * 主机序列号
     */
    private String monitorSn;

    /**
     * 主机时间
     */
    private LocalDateTime gmttimestamp;

    /**
     * 电池电量
     */
    private Integer battery;

    /**
     * 经度
     */
    private Double gpsx;

    /**
     * 纬度
     */
    private Double gpsy;

    /**
     * 温度
     */
    private Double temperature;

    /**
     * 湿度
     */
    private Double humidity;

    /**
     * 气压
     */
    private Double hpa;

    /**
     * 探测器ID
     */
    private String sensorId;

    /**
     * 探测器序列号
     */
    private String sensorSn;

    /**
     * 探测器数据
     */
    private Double sensorData;

    /**
     * 服务器数据插入时间
     */
    private LocalDateTime timeInserted;


}
