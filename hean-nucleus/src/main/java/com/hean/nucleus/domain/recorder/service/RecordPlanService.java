package com.hean.nucleus.domain.recorder.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.annotation.MethodDoc;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.mapper.TCarMapper;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.recorder.mapper.RadpacDevInfoMapper;
import com.hean.nucleus.domain.recorder.mapper.RadpacRecordPlanMapper;
import com.hean.nucleus.domain.recorder.mapper.TRecorderMapper;
import com.hean.nucleus.domain.recorder.model.RadpacDevInfo;
import com.hean.nucleus.domain.recorder.model.RadpacRecordPlan;
import com.hean.nucleus.domain.recorder.model.TRecorder;
import com.hean.nucleus.domain.recorder.request.RecordPlanAddRequest;
import com.hean.nucleus.domain.recorder.request.RecordPlanListRequest;
import com.hean.nucleus.domain.recorder.request.RecordPlanUpdateRequest;
import com.hean.nucleus.domain.recorder.response.RecordPlanResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class RecordPlanService {

    @Resource
    private TCarMapper carMapper;

    @Resource
    private TRecorderMapper recorderMapper;

    @Resource
    private RadpacDevInfoMapper radpacDevInfoMapper;

    @Resource
    private RadpacRecordPlanMapper radpacRecordPlanMapper;

    /**
     * 添加海康NVR录像计划
     *
     * @param request 录像计划信息
     */
    public void add(RecordPlanAddRequest request) {
        // 检查车辆是否存在
        TCar car = carMapper.selectById(request.getCarId());
        if (car == null || car.getDeleteFlag() == 1) {
            throw new ServiceException("车辆不存在");
        }

        TRecorder recorder = recorderMapper.selectOne(s -> s
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getCarBoundFlag, 1)
                .eq(TRecorder::getCarId, request.getCarId())
        );
        if(recorder == null) {
            throw new ServiceException("车辆还未绑定NVR设备");
        }

        // 检查radpac设备是否存在
        RadpacDevInfo existDevice = radpacDevInfoMapper.selectById(recorder.getRadpacRecorderId());
        if (existDevice == null) {
            throw new ServiceException("radpac设备不存在");
        }

        // 检查是否已存在相同通道和星期的录像计划
        RadpacRecordPlan existPlan = radpacRecordPlanMapper.checkDuplicateRecordPlan(
                recorder.getRadpacRecorderId(),
                request.getChannelNum(),
                request.getWeekNum(),
                null);

        if (existPlan != null) {
            throw new ServiceException("该通道在当前星期已存在录像计划");
        }

        RadpacRecordPlan recordPlan = new RadpacRecordPlan();
        BeanUtils.copyProperties(request, recordPlan);
        recordPlan.setRadpacRecorderId(existDevice.getRadpacRecorderId());

        radpacRecordPlanMapper.insert(recordPlan);
    }

    /**
     * 修改海康NVR录像计划
     *
     * @param request 录像计划信息
     */
    public void update(RecordPlanUpdateRequest request) {
        // 检查录像计划是否存在
        RadpacRecordPlan existPlan = radpacRecordPlanMapper.selectById(request.getId());
        if (existPlan == null) {
            throw new ServiceException("录像计划不存在");
        }

        // 如果修改了通道或星期，需要检查是否与其他计划冲突
        if (!existPlan.getChannelNum().equals(request.getChannelNum()) ||
                !existPlan.getWeekNum().equals(request.getWeekNum())) {

            RadpacRecordPlan conflictPlan = radpacRecordPlanMapper.checkDuplicateRecordPlan(
                    existPlan.getRadpacRecorderId(),
                    request.getChannelNum(),
                    request.getWeekNum(),
                    request.getId());

            if (conflictPlan != null) {
                throw new ServiceException("该通道在当前星期已存在录像计划");
            }
        }

        RadpacRecordPlan recordPlan = new RadpacRecordPlan();
        BeanUtils.copyProperties(request, recordPlan);

        radpacRecordPlanMapper.updateById(recordPlan);
    }

    /**
     * 删除录像计划
     *
     * @param request 录像计划ID
     */
    public void delete(IdRequest request) {
        // 检查录像计划是否存在
        RadpacRecordPlan existPlan = radpacRecordPlanMapper.selectById(request.getId());
        if (existPlan == null) {
            throw new ServiceException("录像计划不存在");
        }

        radpacRecordPlanMapper.deleteById(request.getId());
    }

    /**
     * 分页查询录像计划
     *
     * @param request 查询条件
     * @return 录像计划列表
     */
    @MethodDoc(name = "广东省城市放射源收贮海康NVR设备录像计划信息")
    public PageView<RecordPlanResponse> listPage(RecordPlanListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        PageInfo<RecordPlanResponse> pageInfo = new PageInfo<>(radpacRecordPlanMapper.selectRecordPlanList(request));
        return PageView.from(pageInfo, pageInfo.getList());
    }

    /**
     * 查询录像计划详情
     *
     * @param request 录像计划ID
     * @return 录像计划详情
     */
    public RecordPlanResponse detail(IdRequest request) {
        RadpacRecordPlan recordPlan = radpacRecordPlanMapper.selectById(request.getId());
        if (recordPlan == null) {
            throw new ServiceException("录像计划不存在");
        }

        RecordPlanResponse response = new RecordPlanResponse();
        BeanUtils.copyProperties(recordPlan, response);

        RadpacDevInfo deviceInfo = radpacDevInfoMapper.selectById(recordPlan.getRadpacRecorderId());
        if (deviceInfo != null) {
            TRecorder recorder = recorderMapper.selectOne(r -> r
                    .eq(TRecorder::getDeleteFlag, 0)
                    .eq(TRecorder::getRadpacRecorderId, recordPlan.getRadpacRecorderId())
            );
            
            if (recorder != null) {
                TCar car = carMapper.selectById(recorder.getCarId());
                if (car != null && car.getDeleteFlag() == 0) {
                    response.setCarNumber(car.getCarNumber());
                }
            }
        }

        return response;
    }


}
