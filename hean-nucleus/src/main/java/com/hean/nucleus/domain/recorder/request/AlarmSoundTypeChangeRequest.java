package com.hean.nucleus.domain.recorder.request;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
public class AlarmSoundTypeChangeRequest {

    @NotNull(message = "汽车ID不能为空")
    private Long carId;

    //声音类型：0-短叫，1-长叫，2-静音
    @NotNull(message = "声音类型不能为空")
    @Range(min = 0, max = 2, message = "声音类型无效")
    private Integer type;

}
