package com.hean.nucleus.domain.camera.mapper;

import com.hean.nucleus.domain.camera.model.TCamera;
import com.hean.nucleus.domain.camera.request.CameraListRequest;
import com.hean.nucleus.domain.camera.response.CameraListResponse;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;

import java.util.List;

/**
 * <p>
 * 摄像头 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface TCameraMapper extends CoreMapper<TCamera> {

    /**
     * 分页查询摄像头列表
     *
     * @param request 查询条件
     * @return 摄像头列表
     */
    List<CameraListResponse> list(CameraListRequest request);

}
