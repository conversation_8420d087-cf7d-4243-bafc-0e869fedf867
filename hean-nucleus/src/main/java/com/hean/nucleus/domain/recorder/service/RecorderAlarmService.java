package com.hean.nucleus.domain.recorder.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.annotation.MethodDoc;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.mapper.TCarMapper;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.recorder.mapper.RadpacAlarmMapper;
import com.hean.nucleus.domain.recorder.mapper.TRecorderMapper;
import com.hean.nucleus.domain.recorder.model.RadpacAlarm;
import com.hean.nucleus.domain.recorder.model.TRecorder;
import com.hean.nucleus.domain.recorder.request.AlarmProcessAddRequest;
import com.hean.nucleus.domain.recorder.request.RecorderAlarmListRequest;
import com.hean.nucleus.domain.recorder.response.RecorderAlarmResponse;
import com.hean.nucleus.domain.support.mapper.TAlarmProcessMapper;
import com.hean.nucleus.domain.support.model.TAlarmProcess;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class RecorderAlarmService {

    @Resource
    private RadpacAlarmMapper radpacAlarmMapper;

    @Resource
    private TAlarmProcessMapper alarmProcessMapper;

    @Resource
    private TRecorderMapper recorderMapper;

    @Resource
    private TCarMapper carMapper;

    /**
     * 分页查询设备报警
     *
     * @param request 查询条件
     * @return 设备报警列表
     */
    @MethodDoc(name = "广东省城市放射源收贮海康NVR设备报警信息")
    public PageView<RecorderAlarmResponse> listPage(RecorderAlarmListRequest request) {
        if (request.getPageNum() != null && request.getPageSize() != null) {
            PageHelper.startPage(request.getPageNum(), request.getPageSize());
        }
        
        List<RecorderAlarmResponse> list = radpacAlarmMapper.list(request);
        PageInfo<RecorderAlarmResponse> pageInfo = new PageInfo<>(list);

        return PageView.from(pageInfo, list);
    }

    /**
     * 查询设备报警详情
     *
     * @param request 报警ID
     * @return 报警详情
     */
    public RecorderAlarmResponse detail(IdRequest request) {
        RadpacAlarm alarm = radpacAlarmMapper.selectById(request.getId());
        if (alarm == null) {
            throw new ServiceException("设备报警不存在");
        }

        RecorderAlarmResponse response = new RecorderAlarmResponse();
        BeanUtils.copyProperties(alarm, response);

        // 获取车辆信息
        TRecorder recorder = recorderMapper.selectOne(r -> r
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getRadpacRecorderId, alarm.getRadpacRecorderId())
        );
        
        if (recorder != null) {
            TCar car = carMapper.selectById(recorder.getCarId());
            if (car != null && car.getDeleteFlag() == 0) {
                response.setCarId(car.getId());
                response.setCarNumber(car.getCarNumber());
            }
        }

        // 获取处置信息
        TAlarmProcess alarmProcess = alarmProcessMapper.selectOne(ap -> ap
                .eq(TAlarmProcess::getAlarmId, alarm.getAlarmId())
                .eq(TAlarmProcess::getDeleteFlag, 0)
        );
        
        if (alarmProcess != null) {
            response.setProcessFlag(1);
            response.setProcessResult(alarmProcess.getProcessResult());
        } else {
            response.setProcessFlag(0);
        }

        return response;
    }

    /**
     * 告警处置
     *
     * @param request 处置信息
     */
    public void processAlarm(AlarmProcessAddRequest request) {
        // 检查告警是否存在
        RadpacAlarm alarm = radpacAlarmMapper.selectById(request.getAlarmId());
        if (alarm == null) {
            throw new ServiceException("告警不存在");
        }

        // 检查是否已经处置过
        TAlarmProcess existProcess = alarmProcessMapper.selectOne(ap -> ap
                .eq(TAlarmProcess::getAlarmId, request.getAlarmId())
                .eq(TAlarmProcess::getDeleteFlag, 0)
        );

        if (existProcess != null) {
            throw new ServiceException("该告警已经处置过");
        }

        // 创建处置记录
        TAlarmProcess alarmProcess = new TAlarmProcess();
        alarmProcess.setAlarmId(request.getAlarmId());
        alarmProcess.setProcessResult(request.getProcessResult());
        alarmProcess.setProcessTime(request.getProcessTime());
        alarmProcess.setDeleteFlag(0);

        alarmProcessMapper.insert(alarmProcess);
    }

    /**
     * 删除告警处置
     *
     * @param request 告警ID
     */
    public void deleteProcess(IdRequest request) {
        // 检查告警是否存在
        RadpacAlarm alarm = radpacAlarmMapper.selectById(request.getId());
        if (alarm == null) {
            throw new ServiceException("告警不存在");
        }

        // 查找处置记录
        TAlarmProcess alarmProcess = alarmProcessMapper.selectOne(ap -> ap
                .eq(TAlarmProcess::getAlarmId, request.getId())
                .eq(TAlarmProcess::getDeleteFlag, 0)
        );

        if (alarmProcess == null) {
            throw new ServiceException("该告警尚未处置");
        }

        // 软删除处置记录
        alarmProcess.setDeleteFlag(1);
        alarmProcess.setDeleteTime(LocalDateTime.now());
        alarmProcessMapper.updateById(alarmProcess);
    }

}
