package com.hean.nucleus.domain.camera.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CameraUpdateRequest {

    @NotNull(message = "摄像头ID不能为空")
    private Long id;

    /**
     * 摄像头名称
     */
    private String name;

    /**
     * 萤石云摄像头通道序号
     */
    private Integer ys7ChannelNo;

    /**
     * 萤石云摄像头通道名称
     */
    private String ys7ChannelName;

    /**
     * 海康摄像头indexCode
     */
    private String hkIndexCode;

}
