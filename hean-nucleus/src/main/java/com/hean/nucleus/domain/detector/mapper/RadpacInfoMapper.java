package com.hean.nucleus.domain.detector.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.detector.bean.Detector;
import com.hean.nucleus.domain.detector.model.RadpacInfo;
import com.hean.nucleus.domain.detector.request.DetectorListRequest;
import com.hean.nucleus.domain.detector.request.RadpacDetectorListRequest;
import com.hean.nucleus.domain.recorder.request.RadpacRecorderListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 探测器信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacInfoMapper extends CoreMapper<RadpacInfo> {

    List<RadpacInfo> list(@Param("request") RadpacDetectorListRequest request);

}
