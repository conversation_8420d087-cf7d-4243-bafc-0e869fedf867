package com.hean.nucleus.domain.recorder.request;

import com.hean.common.annotation.FieldDoc;
import lombok.Data;

@Data
public class AlarmLogListRequest {

    /** 当前记录起始索引 */
    @FieldDoc(friendName = "页码", required = true, description = "分页页码")
    private Integer pageNum;

    /** 每页显示记录数 */
    @FieldDoc(friendName = "分页大小", required = true, description = "分页大小")
    private Integer pageSize;

    @FieldDoc(friendName = "车辆ID", required = false, description = "车辆ID")
    private Long carId;

}
