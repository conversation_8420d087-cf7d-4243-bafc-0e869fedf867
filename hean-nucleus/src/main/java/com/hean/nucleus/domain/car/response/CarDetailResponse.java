package com.hean.nucleus.domain.car.response;

import com.hean.nucleus.domain.camera.response.CameraStatusResponse;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.detector.response.DetectorResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarDetailResponse extends TCar {

    // 是否已绑定设备，0:未绑定，1:已绑定
    private Integer deviceBoundFlag;

    // 所属分组名称
    private String carGroupName;

    // 探测器
    private List<DetectorResponse> detectors;

    // 摄像头，deviceBoundFlag=1时有值
    private List<CameraStatusResponse> cameras;

    // 布防状态：0-撤防，1-布防，deviceBoundFlag=1时有值
    private Integer defenceSet;

}
