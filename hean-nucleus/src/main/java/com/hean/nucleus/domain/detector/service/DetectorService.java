package com.hean.nucleus.domain.detector.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.detector.bean.Detector;
import com.hean.nucleus.domain.detector.request.*;
import com.hean.nucleus.domain.detector.mapper.RadpacInfoMapper;
import com.hean.nucleus.domain.detector.mapper.TDetectorMapper;
import com.hean.nucleus.domain.detector.model.RadpacInfo;
import com.hean.nucleus.domain.detector.model.TDetector;
import com.hean.nucleus.domain.recorder.model.RadpacDevInfo;
import com.hean.nucleus.domain.recorder.request.RadpacRecorderListRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * RadpacInfo表中的数据相当于探测器资源库记录，表中的数据需要先通过关联添加的方式加入到主表TDetector中才能使用
 */
@Slf4j
@Service
public class DetectorService {

    @Resource
    private TDetectorMapper detectorMapper;

    @Resource
    private RadpacInfoMapper radpacInfoMapper;

    public List<RadpacInfo> listRadpacDetectors(RadpacDetectorListRequest request) {
        return radpacInfoMapper.list(request);
    }

    @Transactional
    public void add(DetectorAddRequest request) {
        // 判断radpac记录是否存在
        RadpacInfo radpacInfo = radpacInfoMapper.selectById(request.getRadpacDetectorId());
        if(radpacInfo == null) {
            throw new ServiceException("radpac探测器记录不存在");
        }

        TDetector detector = new TDetector();
        detector.setRadpacDetectorId(radpacInfo.getRadpacDetectorId());
        detector.setCreateTime(LocalDateTime.now());
        detector.setUpdateTime(LocalDateTime.now());
        detector.setDeleteFlag(0);
        detectorMapper.insert(detector);
    }

    @Transactional
    public void update(DetectorUpdateRequest request) {
        TDetector detector = detectorMapper.selectById(request.getId());
        if(detector == null || detector.getDeleteFlag() == 1) {
            throw new ServiceException("探测器不存在");
        }

        RadpacInfo radpacInfo = radpacInfoMapper.selectById(request.getRadpacDetectorId());
        if(radpacInfo == null) {
            throw new RuntimeException("radpac探测器不存在");
        }

        detector.setRadpacDetectorId(radpacInfo.getRadpacDetectorId());
        detector.setUpdateTime(LocalDateTime.now());
        detectorMapper.updateById(detector);
    }

    @Transactional
    public void bindCar(DetectorBindRequest request) {
        TDetector detector = detectorMapper.selectById(request.getDetectorId());
        if(detector == null || detector.getDeleteFlag() == 1) {
            throw new ServiceException("探测器不存在");
        }

        // 判断探测器是否存在
        RadpacInfo radpacInfo = radpacInfoMapper.selectById(detector.getRadpacDetectorId());
        if(radpacInfo == null) {
            throw new ServiceException("radpac探测器不存在");
        }

        if(detector.getCarBoundFlag() == 1) {
            throw new ServiceException("探测器已绑定车辆");
        }

        // 绑定车辆
        detector.setCarBoundFlag(1);
        detector.setCarId(request.getCarId());
        detector.setUpdateTime(LocalDateTime.now());
        detectorMapper.updateById(detector);
    }

    @Transactional
    public void unbindCar(IdRequest request) {
        TDetector detector = detectorMapper.selectById(request.getId());
        if(detector == null || detector.getDeleteFlag() == 1) {
            throw new ServiceException("设备不存在");
        }

        detector.setCarBoundFlag(0);
        detector.setCarId(null);
        detector.setUpdateTime(LocalDateTime.now());
        detectorMapper.updateById(detector);
    }

    /**
     * 删除探测器，逻辑删除
     * @param request
     */
    @Transactional
    public void delete(IdRequest request) {
        TDetector detector = detectorMapper.selectById(request.getId());
        if(detector == null || detector.getDeleteFlag() == 1) {
            throw new ServiceException("设备不存在");
        }

        detector.setDeleteFlag(1);
        detector.setDeleteTime(LocalDateTime.now());
        detectorMapper.updateById(detector);
    }


    public List<Detector> listByCarId(Long carId) {
        List<TDetector> tDetectors = detectorMapper.selectList(s -> s
                .eq(TDetector::getDeleteFlag, 0)
                .eq(TDetector::getCarBoundFlag, 1)
                .eq(TDetector::getCarId, carId)
        );

        Set<Long> radpacIds = tDetectors.stream().map(TDetector::getRadpacDetectorId).collect(Collectors.toSet());
        List<RadpacInfo> radpacInfos;
        if(!radpacIds.isEmpty()) {
            radpacInfos = radpacInfoMapper.selectList(s -> s
                    .in(RadpacInfo::getRadpacDetectorId, radpacIds)
            );
        } else {
            radpacInfos = new ArrayList<>();
        }

        return tDetectors.stream().map(s -> {
            Detector detector = new Detector();
            BeanUtils.copyProperties(s, detector);
            for (RadpacInfo radpacInfo : radpacInfos) {
                if(s.getRadpacDetectorId().equals(radpacInfo.getRadpacDetectorId())) {
                    BeanUtils.copyProperties(radpacInfo, detector);
                }
            }
            return detector;
        }).collect(Collectors.toList());
    }


    public PageView<Detector> listPage(DetectorListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        PageInfo<Detector> pageInfo = new PageInfo<>(detectorMapper.list(request));
        return PageView.from(pageInfo, pageInfo.getList());
    }

    // 详情
    public Detector detail(IdRequest request) {
        TDetector dbDetector = detectorMapper.selectById(request.getId());
        if(dbDetector == null || dbDetector.getDeleteFlag() == 1) {
            throw new ServiceException("探测器不存在");
        }

        RadpacInfo radpacInfo = radpacInfoMapper.selectById(dbDetector.getRadpacDetectorId());
        if(radpacInfo == null) {
            throw new RuntimeException("radpac探测器不存在");
        }

        Detector detector = new Detector();
        BeanUtils.copyProperties(radpacInfo, detector);
        BeanUtils.copyProperties(dbDetector, detector);

        return detector;
    }
}
