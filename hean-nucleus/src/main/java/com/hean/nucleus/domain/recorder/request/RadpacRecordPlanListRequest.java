package com.hean.nucleus.domain.recorder.request;

import com.hean.common.annotation.FieldDoc;
import lombok.Data;

/**
 * 海康NVR录像计划分页查询请求
 */
@Data
public class RadpacRecordPlanListRequest {

    /**
     * 当前记录起始索引
     */
    @FieldDoc(friendName = "页码", required = true, description = "分页页码")
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    @FieldDoc(friendName = "分页大小", required = true, description = "分页大小")
    private Integer pageSize;

    /**
     * 通道号
     */
    @FieldDoc(friendName = "通道号", required = false, description = "通道号")
    private Integer channelNum;

    /**
     * 星期
     */
    @FieldDoc(friendName = "星期", required = false, description = "星期")
    private Integer weekNum;

    /**
     * 设备ID
     */
    @FieldDoc(friendName = "设备ID", required = false, description = "设备ID")
    private String deviceId;
}
