package com.hean.nucleus.domain.recorder.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * radpac海康NVR设备日志描述字典表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("radpac_dev_log_detial")
public class RadpacDevLogDetial {

    /**
     * 日志记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 日志类型值；
     * 关联radpac_dev_log表MINOR_TYPE字段
     */
    private String value;

    /**
     * 日志主类型，1：报警；2：异常；3：操作；4：信息；其它：未知；
     * 关联radpac_dev_log表MAJOR_TYPE字段
     */
    private Integer majorType;

    /**
     * 日志详情描述
     */
    private String describe;

}
