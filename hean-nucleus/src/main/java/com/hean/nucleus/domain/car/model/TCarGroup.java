package com.hean.nucleus.domain.car.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * <p>
 * 汽车分组表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_car_group")
public class TCarGroup {

    /**
     * 车辆分组ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父分组ID
     */
    private Long parentId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记：1=已删除，0=未删除
     */
    private Integer deleteFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

    /**
     * 后台删除操作用户
     */
    private Long deleteSysUserId;


}
