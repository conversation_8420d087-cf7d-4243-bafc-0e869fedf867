package com.hean.nucleus.domain.recorder.service;

import com.hean.nucleus.domain.recorder.mapper.RadpacAlarmMapper;
import com.hean.nucleus.domain.support.mapper.TAlarmProcessMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 告警处置服务
 */
@Slf4j
@Service
public class AlarmProcessService {

    @Resource
    private RadpacAlarmMapper radpacAlarmMapper;

    @Resource
    private TAlarmProcessMapper alarmProcessMapper;


}
