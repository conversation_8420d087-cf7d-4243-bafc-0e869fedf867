package com.hean.nucleus.domain.detector.response;

import com.hean.common.annotation.FieldDoc;
import com.hean.nucleus.domain.detector.bean.Detector;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DetectorResponse extends Detector {

    //1:分组,2:车辆,3:探测器,4:摄像头
    @FieldDoc(friendName = "数据类型", required = true, description = "数据类型(1:分组/2:车辆/3:探测器/4:摄像头)")
    private Integer dataType = 3;

}
