package com.hean.nucleus.domain.recorder.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * NVR设备表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_recorder")
public class TRecorder {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * RADPAC_DEV_INFO表主键DEVICE_ID
     */
    private String radpacRecorderId;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 萤石云关联的设备序号
     */
    private String ys7DeviceSerial;

    /**
     * 是否已绑定车辆，0:未绑定，1:已绑定
     */
    private Integer carBoundFlag;

    /**
     * 绑定的车辆ID
     */
    private Long carId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记：1=已删除，0=未删除
     */
    private Integer deleteFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

    /**
     * 后台删除操作用户
     */
    private Long deleteSysUserId;


}
