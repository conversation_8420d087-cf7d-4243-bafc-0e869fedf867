package com.hean.nucleus.domain.car.response;

import com.hean.nucleus.domain.car.model.TCarGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarGroupNodeResponse extends TCarGroup {

    private String parentName;

    private List<CarGroupNodeResponse> children = new ArrayList<>();

    private List<CarDetailResponse> cars = new ArrayList<>();

}
