package com.hean.nucleus.domain.detector.bean;

import com.hean.nucleus.domain.detector.model.TDetector;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * RadpacInfo表中的数据相当于探测器资源库记录，表中的数据需要先通过关联的方式加入到主表TDetector中才能使用
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Detector extends TDetector {

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 主机通讯编号
     */
    private String commId;

    /**
     * 主机序列号
     */
    private String monitorSn;

    /**
     * 探测器序列号
     */
    private String sensorSn;

    /**
     * 探测器ID
     */
    private String sensorId;

    /**
     * 主机时间
     */
    private LocalDateTime gmttimestamp;

    /**
     * 探测器数据
     */
    private Double sensorData;

    /**
     * 数据单位
     */
    private String dataUnit;

    /**
     * 设备状态
     */
    private Integer sensorStatus;

    /**
     * 设备IP
     */
    private String monitorIp;

    /**
     * 设备端口
     */
    private Integer monitorPort;

    /**
     * 低阈值
     */
    private Double lowLimit;

    /**
     * 高阈值
     */
    private Double highLimit;

    /**
     * 数据上传周期
     */
    private Integer dutyCycle;

}
