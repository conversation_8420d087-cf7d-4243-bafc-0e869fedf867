package com.hean.nucleus.domain.camera.response;

import com.hean.common.annotation.FieldDoc;
import com.hean.nucleus.domain.camera.model.TCamera;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CameraDetailResponse extends TCamera {

    /**
     * 车辆车牌号
     */
    @FieldDoc(friendName = "车牌号", required = false, description = "车辆车牌号")
    private String carNumber;

    /**
     * 车辆所属分组名称
     */
    @FieldDoc(friendName = "车辆分组名称", required = false, description = "车辆所属分组名称")
    private String carGroupName;

    /**
     * 录像机名称
     */
    @FieldDoc(friendName = "录像机名称", required = false, description = "录像机名称")
    private String recorderName;

    /**
     * 录像机序列号
     */
    @FieldDoc(friendName = "录像机序列号", required = false, description = "录像机序列号")
    private String recorderSerial;

    /**
     * 萤石云设备序列号
     */
    @FieldDoc(friendName = "萤石云设备序列号", required = false, description = "萤石云设备序列号")
    private String ys7DeviceSerial;

    /**
     * 在线状态：0-不在线，1-在线
     */
    @FieldDoc(friendName = "在线状态", required = false, description = "在线状态：0-不在线，1-在线")
    private Integer status = 0;

}
