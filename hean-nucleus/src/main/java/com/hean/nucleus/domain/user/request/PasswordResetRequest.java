package com.hean.nucleus.domain.user.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PasswordResetRequest {

    @NotNull(message = "请指定用户")
    private Long userId;

    @NotBlank(message = "请输入密码")
    @Min(value = 8, message = "密码长度不能小于8位")
    private String password;

}
