package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.model.RadpacRecordPlan;
import com.hean.nucleus.domain.recorder.request.RecordPlanListRequest;
import com.hean.nucleus.domain.recorder.response.RecordPlanResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 海康NVR录像计划设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacRecordPlanMapper extends CoreMapper<RadpacRecordPlan> {

    /**
     * 检查是否存在相同通道和星期的录像计划
     *
     * @param radpacRecorderId radpac设备ID
     * @param channelNum 通道号
     * @param weekNum 星期
     * @param id 录像计划ID，更新时使用，排除自身
     * @return 录像计划
     */
    RadpacRecordPlan checkDuplicateRecordPlan(
            @Param("radpacRecorderId") String radpacRecorderId,
            @Param("channelNum") Integer channelNum,
            @Param("weekNum") Integer weekNum,
            @Param("id") Long id
    );

    /**
     * 分页查询录像计划列表（关联车辆表）
     *
     * @param request 查询条件
     * @return 录像计划列表
     */
    List<RecordPlanResponse> selectRecordPlanList(RecordPlanListRequest request);

}
