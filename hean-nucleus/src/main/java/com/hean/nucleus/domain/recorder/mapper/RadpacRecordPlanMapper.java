package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.model.RadpacRecordPlan;

/**
 * <p>
 * 海康NVR录像计划设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacRecordPlanMapper extends CoreMapper<RadpacRecordPlan> {

}
