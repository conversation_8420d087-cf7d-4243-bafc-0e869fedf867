package com.hean.nucleus.domain.detector.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 海康NVR设备添加请求
 */
@Data
public class DetectorAddRequest {

    /**
     * radpac表主键id
     */
    @NotNull(message = "探测器id不能为空")
    private Long radpacDetectorId;

    @NotBlank(message = "探测器名称不能为空")
    private String name;

}
