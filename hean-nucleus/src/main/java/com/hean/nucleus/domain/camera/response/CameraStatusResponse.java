package com.hean.nucleus.domain.camera.response;

import com.hean.common.annotation.FieldDoc;
import com.hean.nucleus.domain.camera.model.TCamera;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CameraStatusResponse extends TCamera {

    // 在线状态：0-不在线，1-在线
    @FieldDoc(friendName = "在线状态", required = true, description = "在线状态：0-不在线，1-在线")
    private Integer status = 0;

}
