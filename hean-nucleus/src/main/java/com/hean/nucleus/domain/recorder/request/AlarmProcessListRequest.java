package com.hean.nucleus.domain.recorder.request;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警处置分页查询请求
 */
@Data
public class AlarmProcessListRequest {

    /**
     * 当前记录起始索引
     */
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    private Integer pageSize;

    /**
     * 告警ID
     */
    private Long alarmId;

    /**
     * 处置用户ID
     */
    private String processUserId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
