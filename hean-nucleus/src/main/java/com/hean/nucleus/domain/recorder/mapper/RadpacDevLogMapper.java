package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.model.RadpacDevLog;

/**
 * <p>
 * 海康NVR设备日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacDevLogMapper extends CoreMapper<RadpacDevLog> {

}
