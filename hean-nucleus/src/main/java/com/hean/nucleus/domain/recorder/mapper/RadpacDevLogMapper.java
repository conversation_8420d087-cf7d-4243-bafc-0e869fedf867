package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.model.RadpacDevLog;
import com.hean.nucleus.domain.recorder.request.RecorderLogListRequest;
import com.hean.nucleus.domain.recorder.response.RecorderLogResponse;

import java.util.List;

/**
 * <p>
 * 海康NVR设备日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacDevLogMapper extends CoreMapper<RadpacDevLog> {

    /**
     * 分页查询设备日志列表
     *
     * @param request 查询条件
     * @return 设备日志列表
     */
    List<RecorderLogResponse> list(RecorderLogListRequest request);

}
