package com.hean.nucleus.domain.ys7.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class Ys7ListPageRequest {

    /** 分页起始页，从1开始 */
    @NotNull(message = "分页起始页不能为空")
    @Min(value = 1, message = "分页起始页不能小于1")
    private Integer pageNum;

    /** 分页大小，默认为10，最大为50 */
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize;

}
