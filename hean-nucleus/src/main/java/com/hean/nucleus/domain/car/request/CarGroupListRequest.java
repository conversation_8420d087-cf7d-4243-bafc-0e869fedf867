package com.hean.nucleus.domain.car.request;

import com.hean.common.annotation.FieldDoc;
import lombok.Data;

@Data
public class CarGroupListRequest {

    /** 当前记录起始索引 */
    @FieldDoc(friendName = "页码", required = true, description = "分页页码")
    private Integer pageNum;

    /** 每页显示记录数 */
    @FieldDoc(friendName = "分页大小", required = true, description = "分页大小")
    private Integer pageSize;

    @FieldDoc(friendName = "父级分组ID", required = false, description = "父级分组ID")
    private Long parentId = 0L;

    @FieldDoc(friendName = "分组名称", required = false, description = "分组名称")
    private String name;
}
