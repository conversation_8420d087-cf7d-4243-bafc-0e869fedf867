package com.hean.nucleus.domain.recorder.bean;

import com.hean.nucleus.domain.recorder.model.TRecorder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * RadpacDevInfo表中的数据相当于NVR设备资源库记录，表中的数据需要先通过关联的方式加入到主表TRecorder中才能使用
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Recorder extends TRecorder {

    /**
     * 硬盘编号
     */
    private Integer dskNum;

    /**
     * 硬盘大小，单位MB
     */
    private Integer dskSize;

    /**
     * 硬盘剩余容量，单位MB
     */
    private Integer dskRemain;

    /**
     * 布防设置，0：撤防、1：布防
     */
    private Integer defenseSet;

    /**
     * 车牌号
     */
    private String carNumber;

}
