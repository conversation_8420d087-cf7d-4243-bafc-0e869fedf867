package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.bean.Recorder;
import com.hean.nucleus.domain.recorder.model.RadpacDevInfo;
import com.hean.nucleus.domain.recorder.request.RadpacRecorderListRequest;
import com.hean.nucleus.domain.recorder.request.RecorderListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 海康设备日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacDevInfoMapper extends CoreMapper<RadpacDevInfo> {

    List<RadpacDevInfo> list(@Param("request") RadpacRecorderListRequest request);

}
