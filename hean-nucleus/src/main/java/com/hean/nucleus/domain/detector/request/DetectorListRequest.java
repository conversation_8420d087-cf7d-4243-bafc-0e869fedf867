package com.hean.nucleus.domain.detector.request;

import com.hean.common.annotation.FieldDoc;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * 探测器查询请求
 */
@Data
public class DetectorListRequest {

    /**
     * 当前记录起始索引
     */
    @FieldDoc(friendName = "页码", required = true, description = "分页页码")
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    @FieldDoc(friendName = "分页大小", required = true, description = "分页大小")
    private Integer pageSize;

    /**
     * 主机通讯编号
     */
    private String commId;

    /**
     * 主机序列号
     */
    private String monitorSn;

    /**
     * 探测器序列号
     */
    private String sensorSn;

    /**
     * 探测器ID
     */
    private String sensorId;

    /**
     * 是否已绑定车辆，0:未绑定，1:已绑定
     */
    @FieldDoc(friendName = "绑定状态", required = false, description = "是否已绑定车辆，0:未绑定，1:已绑定")
    @Range(min = 0, max = 1, message = "绑定状态无效")
    private Integer carBoundFlag;

    @FieldDoc(friendName = "车辆ID", required = false, description = "车辆ID")
    private Long carId;

    /**
     * 车牌号
     */
    @FieldDoc(friendName = "车牌号", required = false, description = "车牌号")
    private String carNumber;

}
