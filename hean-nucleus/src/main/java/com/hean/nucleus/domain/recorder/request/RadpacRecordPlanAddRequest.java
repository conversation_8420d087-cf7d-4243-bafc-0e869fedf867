package com.hean.nucleus.domain.recorder.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 海康NVR录像计划添加请求
 */
@Data
public class RadpacRecordPlanAddRequest {

    /**
     * 通道号(两个摄像头，1和2)
     */
    @NotNull(message = "通道号不能为空")
    @Min(value = 1, message = "通道号最小为1")
    @Max(value = 2, message = "通道号最大为2")
    private Integer channelNum;

    /**
     * 星期一到日(0-6)
     */
    @NotNull(message = "星期不能为空")
    @Min(value = 0, message = "星期最小为0")
    @Max(value = 6, message = "星期最大为6")
    private Integer weekNum;

    /**
     * 是否启用录像(0、1)
     */
    @NotNull(message = "是否启用录像不能为空")
    @Min(value = 0, message = "是否启用录像值为0或1")
    @Max(value = 1, message = "是否启用录像值为0或1")
    private Integer isUse;

    /**
     * 延录时间
     */
    @NotNull(message = "延录时间不能为空")
    private Integer recordTime;

    /**
     * 预录时间
     */
    @NotNull(message = "预录时间不能为空")
    private Integer preRecordTime;

    /**
     * 是否全天录像
     */
    @NotNull(message = "是否全天录像不能为空")
    @Min(value = 0, message = "是否全天录像值为0或1")
    @Max(value = 1, message = "是否全天录像值为0或1")
    private Integer isAllday;

    /**
     * 录像触发类型
     */
    @NotNull(message = "录像触发类型不能为空")
    private Integer recordType;

    /**
     * 开始时间1
     */
    private String startTime1;

    /**
     * 结束时间1
     */
    private String stopTime1;

    /**
     * 开始时间2
     */
    private String startTime2;

    /**
     * 结束时间2
     */
    private String stopTime2;

    /**
     * 是否下发（1需要下发，0已下发）
     */
    @NotNull(message = "是否下发不能为空")
    @Min(value = 0, message = "是否下发值为0或1")
    @Max(value = 1, message = "是否下发值为0或1")
    private Integer downLoad;

    /**
     * 设备ID
     */
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;
}
