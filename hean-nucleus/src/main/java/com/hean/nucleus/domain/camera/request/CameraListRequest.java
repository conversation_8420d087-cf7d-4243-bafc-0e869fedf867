package com.hean.nucleus.domain.camera.request;

import com.hean.common.annotation.FieldDoc;
import lombok.Data;

@Data
public class CameraListRequest {

    /** 当前记录起始索引 */
    @FieldDoc(friendName = "页码", required = true, description = "分页页码")
    private Integer pageNum;

    /** 每页显示记录数 */
    @FieldDoc(friendName = "分页大小", required = true, description = "分页大小")
    private Integer pageSize;

    /**
     * 摄像头名称
     */
    @FieldDoc(friendName = "摄像头名称", required = false, description = "摄像头名称")
    private String name;

    /**
     * 车辆ID
     */
    @FieldDoc(friendName = "车辆ID", required = false, description = "车辆ID")
    private Long carId;

    /**
     * 录像机ID
     */
    @FieldDoc(friendName = "录像机ID", required = false, description = "录像机ID")
    private Long recorderId;

    /**
     * 是否已绑定到NVR，0:未绑定，1:已绑定
     */
    @FieldDoc(friendName = "绑定状态", required = false, description = "是否已绑定到NVR，0:未绑定，1:已绑定")
    private Integer recorderBoundFlag;

    /**
     * 萤石云摄像头通道序号
     */
    @FieldDoc(friendName = "萤石云通道序号", required = false, description = "萤石云摄像头通道序号")
    private Integer ys7ChannelNo;

    /**
     * 萤石云摄像头通道名称
     */
    @FieldDoc(friendName = "萤石云通道名称", required = false, description = "萤石云摄像头通道名称")
    private String ys7ChannelName;

    /**
     * 海康摄像头indexCode
     */
    @FieldDoc(friendName = "海康indexCode", required = false, description = "海康摄像头indexCode")
    private String hkIndexCode;

}
