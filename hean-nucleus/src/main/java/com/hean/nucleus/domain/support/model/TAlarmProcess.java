package com.hean.nucleus.domain.support.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警处置记录表
 */
@Data
@TableName("t_alarm_process")
public class TAlarmProcess {

    @TableId(value = "alarm_id", type = IdType.AUTO)
    private Long alarmId;

    private String processResult;

    private Long processUserId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime processTime;

    /**
     * 删除标记：1=已删除，0=未删除
     */
    private Integer deleteFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

}
