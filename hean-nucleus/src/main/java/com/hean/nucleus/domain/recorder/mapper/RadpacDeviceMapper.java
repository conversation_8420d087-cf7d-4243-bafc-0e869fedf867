package com.hean.nucleus.domain.recorder.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.recorder.model.RadpacDevInfo;
import com.hean.nucleus.domain.recorder.model.RadpacDevLog;
import com.hean.nucleus.domain.recorder.model.RadpacRecordPlan;
import com.hean.nucleus.domain.recorder.request.RecorderListRequest;
import com.hean.nucleus.domain.recorder.request.RadpacDevLogListRequest;
import com.hean.nucleus.domain.recorder.request.RadpacRecordPlanListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 海康NVR设备 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface RadpacDeviceMapper extends CoreMapper<RadpacDevInfo> {

    /**
     * 分页查询设备列表
     *
     * @param request 查询条件
     * @return 设备列表
     */
    List<RadpacDevInfo> selectDeviceList(@Param("request") RecorderListRequest request);

    /**
     * 查询设备详情
     *
     * @param deviceId 设备ID
     * @return 设备详情
     */
    RadpacDevInfo selectDeviceDetail(@Param("deviceId") String deviceId);

    /**
     * 分页查询录像计划列表
     *
     * @param request 查询条件
     * @return 录像计划列表
     */
    List<RadpacRecordPlan> selectRecordPlanList(@Param("request") RadpacRecordPlanListRequest request);

    /**
     * 查询录像计划详情
     *
     * @param id 录像计划ID
     * @return 录像计划详情
     */
    RadpacRecordPlan selectRecordPlanDetail(@Param("id") Long id);

    /**
     * 检查是否存在相同通道和星期的录像计划
     *
     * @param deviceId 设备ID
     * @param channelNum 通道号
     * @param weekNum 星期
     * @param id 录像计划ID，更新时使用，排除自身
     * @return 录像计划
     */
    RadpacRecordPlan checkDuplicateRecordPlan(@Param("deviceId") String deviceId,
                                            @Param("channelNum") Integer channelNum,
                                            @Param("weekNum") Integer weekNum,
                                            @Param("id") Long id);

    /**
     * 分页查询设备日志列表
     *
     * @param request 查询条件
     * @return 设备日志列表
     */
    List<RadpacDevLog> selectDevLogList(@Param("request") RadpacDevLogListRequest request);
}
