package com.hean.nucleus.domain.car.response;

import com.hean.common.annotation.FieldDoc;
import com.hean.nucleus.domain.car.model.TCarGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarGroupListResponse extends TCarGroup {

    //1:分组,2:车辆,3:探测器,4:摄像头
    @FieldDoc(friendName = "数据类型", required = true, description = "数据类型(1:分组/2:车辆/3:探测器/4:摄像头)")
    private Integer dataType = 1;

    @FieldDoc(friendName = "子分组数量", required = true, description = "子分组数量")
    private Integer childrenSize;

}
