package com.hean.nucleus.domain.ys7.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Ys7WebhookMessage {

    /**
     * 消息头
     */
    private Ys7WebhookMessageHeader header;

    /**
     * 消息体
     */
    private Ys7WebhookMessageBody body;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ys7WebhookMessageHeader {

        /**
         * 消息id
         */
        private String messageId;

        /**
         * 设备序列号
         */
        private String deviceId;

        /**
         * 消息类型，需向消息管道服务申请
         */
        private String type;

        /**
         * 通道号
         */
        private Integer channelNo;

        /**
         * 消息推送时间
         */
        private Long messageTime;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ys7WebhookMessageBody {
        private String payload;
    }
}
