package com.hean.nucleus.domain.user.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.core.domain.model.LoginUser;
import com.hean.common.exception.ServiceException;
import com.hean.common.service.CipherService;
import com.hean.common.utils.SecurityUtils;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.domain.user.mapper.TUserMapper;
import com.hean.nucleus.domain.user.model.TUser;
import com.hean.nucleus.domain.user.request.PasswordResetRequest;
import com.hean.nucleus.domain.user.request.UserAddRequest;
import com.hean.nucleus.domain.user.request.UserListRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserService {

    @Resource
    private TUserMapper userMapper;

    @Resource
    private CipherService cipherService;

    //用户端登录
    public TUser login(String username, String password) {
        if(StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
            throw new ServiceException("请输入有效的用户名和密码");
        }

        TUser model = selectByUsername(username);

        if(model == null) {
            throw new ServiceException("请输入有效的用户名");
        }

        if(!SecurityUtils.matchesPassword(password.trim(), model.getPassword())) {
            throw new ServiceException("密码错误");
        }

        return model;
    }

    public PageView<TUser> listUser(UserListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<TUser> s = new LambdaQueryWrapper<TUser>().eq(TUser::getDeleteFlag, 0);
        if(request.getUsername() != null) {
            s.eq(TUser::getUsername, cipherService.sm4Encrypt(request.getUsername()));
        }
        PageInfo<TUser> pageInfo = new PageInfo<>(userMapper.selectList(s));
        return PageView.from(pageInfo, pageInfo.getList().stream().peek(m -> {
            log.info("查询用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", m.joinItemHash(), m.getItemHash());

            // 核验商密密文HASH
            cipherService.checkHash(m.joinItemHash(), m.getItemHash());

            log.info("查询用户 --> 解密用户名密文：{}", m.getUsername());
            m.setUsername(cipherService.sm4Decrypt(m.getUsername()));
            log.info("查询用户 --> 解密后用户名明文：{}", m.getUsername());
        }).collect(Collectors.toList()));
    }


    //管理端重置用户密码
    public void resetPassword(PasswordResetRequest request) {
        TUser model = userMapper.selectById(request.getUserId());
        if(model == null) {
            throw new ServiceException("未找到用户记录");
        }

        log.info("重置用户密码 --> 待加密密码：{}", request.getPassword());
        model.setPassword(SecurityUtils.encryptPassword(request.getPassword().trim()));
        log.info("重置用户密码 --> 密码密文：{}", model.getPassword());
        model.setItemHash(cipherService.calcHash(model.joinItemHash()));
        log.info("重置用户密码 --> 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", model.joinItemHash(), model.getItemHash());

        userMapper.updateById(model);
    }

    public TUser selectByUsername(String username) {
        String encryptUsername = cipherService.sm4Encrypt(username);
        TUser user = userMapper.selectFirst(s -> s
                .eq(TUser::getUsername, encryptUsername)
                .eq(TUser::getDeleteFlag, 0)
        );

        if(user == null) {
            return null;
        }

        // 核验商密密文HASH
        cipherService.checkHash(user.joinItemHash(), user.getItemHash());

        user.setUsername(cipherService.sm4Decrypt(user.getUsername()));

        return user;
    }

    public TUser detailUser(Long userId) {
        TUser model = userMapper.selectById(userId);
        if(model == null) {
            throw new ServiceException("未找到用户记录");
        }

        log.info("查询用户 --> 核验数据库中保存的密文是否已被篡改 --> 数据库中保存的密文链接字符串：{}, 数据库中保存的HASH：{}", model.joinItemHash(), model.getItemHash());

        // 核验商密密文HASH
        cipherService.checkHash(model.joinItemHash(), model.getItemHash());

        log.info("查询用户 --> 解密用户名密文：{}", model.getUsername());
        model.setUsername(cipherService.sm4Decrypt(model.getUsername()));
        log.info("查询用户 --> 解密后用户名明文：{}", model.getUsername());

        return model;
    }

    public void delete(LoginUser loginUser, Long projectId) {
        TUser model = userMapper.selectById(projectId);
        if(model == null) {
            throw new ServiceException("未找到用户记录");
        }

        if(model.getDeleteFlag() == 1) {
            return;
        }

        model.setDeleteFlag(1);
        model.setDeleteTime(LocalDateTime.now());
        model.setDeleteSysUserId(loginUser.getUserId());
        userMapper.updateById(model);
    }

    public void addUser(UserAddRequest request) {
        TUser model = new TUser();

        log.info("新增用户 --> 待加密用户名：{}", request.getUsername());
        model.setUsername(cipherService.sm4Encrypt(request.getUsername()));
        log.info("新增用户 --> 用户名密文：{}", model.getUsername());
        log.info("新增用户 --> 待加密密码：{}", request.getPassword());
        model.setPassword(SecurityUtils.encryptPassword(request.getPassword().trim()));
        log.info("新增用户 --> 密码密文：{}", model.getPassword());

        model.setItemHash(cipherService.calcHash(model.joinItemHash()));
        log.info("新增用户 --> 密文拼接字符串：{}, 密文拼接后计算的HASH: {}", model.joinItemHash(), model.getItemHash());
        model.setCreateTime(LocalDateTime.now());
        model.setUpdateTime(LocalDateTime.now());
        userMapper.insert(model);
    }

}
