package com.hean.nucleus.domain.recorder.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 海康NVR设备日志分页查询请求
 */
@Data
public class RecorderLogListRequest {

    /**
     * 当前记录起始索引
     */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize;

    /**
     * 车辆ID
     */
    private Long carId;

    /**
     * 日志主类型
     */
    private Integer majorType;

    /**
     * 日志次类型
     */
    private String minorType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
