package com.hean.nucleus.domain.recorder.response;

import com.hean.nucleus.domain.recorder.model.RadpacDevLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 海康NVR设备日志响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecorderLogResponse extends RadpacDevLog {

    /**
     * 车辆ID
     */
    private Long carId;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 日志描述
     */
    private String description;

}
