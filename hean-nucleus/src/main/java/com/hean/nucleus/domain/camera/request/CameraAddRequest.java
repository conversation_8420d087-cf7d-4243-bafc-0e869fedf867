package com.hean.nucleus.domain.camera.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CameraAddRequest {

    @NotNull(message = "车辆id不能为空")
    private Long carId;

    @NotBlank(message = "海康摄像头indexCode不能为空")
    private String hkIndexCode;

    /**
     * 萤石云摄像头通道序号
     */
    @NotNull(message = "萤石云摄像头通道序号不能为空")
    private Integer ys7ChannelNo;

    /**
     * 萤石云摄像头通道名称
     */
    @NotBlank(message = "萤石云摄像头通道名称不能为空")
    private String ys7ChannelName;

}
