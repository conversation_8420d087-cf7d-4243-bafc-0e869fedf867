package com.hean.nucleus.domain.car.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.annotation.MethodDoc;
import com.hean.common.core.domain.model.LoginUser;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.domain.recorder.bean.Recorder;
import com.hean.nucleus.domain.ys7.bean.Ys7Channel;
import com.hean.nucleus.domain.camera.mapper.TCameraMapper;
import com.hean.nucleus.domain.car.mapper.TCarGroupMapper;
import com.hean.nucleus.domain.car.mapper.TCarMapper;
import com.hean.nucleus.domain.camera.model.TCamera;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.car.model.TCarGroup;
import com.hean.nucleus.domain.car.request.CarListRequest;
import com.hean.nucleus.domain.car.request.CarSaveRequest;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.camera.response.CameraStatusResponse;
import com.hean.nucleus.domain.car.response.CarDetailResponse;
import com.hean.nucleus.domain.car.response.CarListResponse;
import com.hean.nucleus.domain.detector.response.DetectorResponse;
import com.hean.nucleus.domain.detector.service.DetectorService;
import com.hean.nucleus.domain.recorder.service.RecorderService;
import com.hean.nucleus.domain.ys7.service.Ys7Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CarService {

    @Resource
    private TCarGroupMapper carGroupMapper;

    @Resource
    private TCarMapper carMapper;

    @Resource
    private RecorderService recorderService;

    @Resource
    private DetectorService detectorService;

    @Resource
    private TCameraMapper cameraMapper;

    @Resource
    private Ys7Service ys7Service;

    //添加车辆
    public void add(CarSaveRequest request) {
        //检查分组是否存在
        TCarGroup group = carGroupMapper.selectById(request.getCarGroupId());
        if (group == null || group.getDeleteFlag() == 1) {
            throw new ServiceException("车辆分组不存在");
        }

        TCar model = new TCar();
        BeanUtils.copyProperties(request, model);

        model.setCreateTime(LocalDateTime.now());
        model.setUpdateTime(LocalDateTime.now());
        model.setDeleteFlag(0);
        carMapper.insert(model);
    }

    //删除车辆
    public void delete(LoginUser loginUser, Long id) {
        TCar model = carMapper.selectById(id);
        if (model == null || model.getDeleteFlag() == 1) {
            throw new ServiceException("车辆不存在");
        }
        model.setDeleteFlag(1);
        model.setUpdateTime(LocalDateTime.now());
        model.setDeleteSysUserId(loginUser.getUserId());
        carMapper.updateById(model);
    }

    //修改车辆
    public void update(CarSaveRequest request) {
        TCar model = carMapper.selectById(request.getId());
        if (model == null || model.getDeleteFlag() == 1) {
            throw new ServiceException("车辆不存在");
        }

        //检查车辆分组是否存在
        TCarGroup group = carGroupMapper.selectById(request.getCarGroupId());
        if (group == null || group.getDeleteFlag() == 1) {
            throw new ServiceException("车辆分组不存在");
        }

        BeanUtils.copyProperties(request, model);

        model.setUpdateTime(LocalDateTime.now());
        carMapper.updateById(model);
    }

    //查询车辆分页列表
    public List<CarListResponse> list(CarListRequest request) {
        return carMapper.list(request);
    }

    //查询车辆分页列表
    @MethodDoc(name = "广东省城市放射源收贮车辆信息")
    public PageView<CarListResponse> listPage(CarListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        PageInfo<CarListResponse> pageInfo = new PageInfo<>(carMapper.list(request));
        return PageView.from(pageInfo, pageInfo.getList());
    }

    //获取车辆详情
    public CarDetailResponse detail(IdRequest req) {
        TCar car = carMapper.selectById(req.getId());
        if (car == null || car.getDeleteFlag() == 1) {
            throw new ServiceException("车辆不存在");
        }

        TCarGroup group = carGroupMapper.selectById(car.getCarGroupId());
        if (group == null) {
            throw new ServiceException("车辆分组不存在");
        }

        CarDetailResponse response = new CarDetailResponse();
        BeanUtils.copyProperties(car, response);
        response.setCarGroupName(group.getName());

        //探测器
        response.setDetectors(detectorService.listByCarId(req.getId()).stream().map(s -> {
            DetectorResponse sr = new DetectorResponse();
            BeanUtils.copyProperties(s, sr);
            return sr;
        }).collect(Collectors.toList()));

        //NVR设备
        Recorder recorder = recorderService.queryByCarId(req.getId());
        if(recorder == null) {
            response.setDeviceBoundFlag(0);
            return response;
        } else {
            response.setDeviceBoundFlag(1);
        }

        //摄像头
        List<CameraStatusResponse> cameras = cameraMapper.selectList(s -> s
                .eq(TCamera::getDeleteFlag, 0)
                .eq(TCamera::getRecorderBoundFlag, 1)
                .eq(TCamera::getCarId, req.getId())
        ).stream().map(s -> {
            CameraStatusResponse cr = new CameraStatusResponse();
            BeanUtils.copyProperties(s, cr);
            return cr;
        }).collect(Collectors.toList());

        List<Ys7Channel> channels = ys7Service.getCameraResourceDetails(recorder.getYs7DeviceSerial());
        for (CameraStatusResponse camera : cameras) {
            channels.stream().filter(d -> Objects.equals(d.getChannelNo(), camera.getYs7ChannelNo())).findFirst().ifPresent(d -> camera.setStatus(d.getStatus()));
        }

        response.setCameras(cameras);


        return response;
    }
}
