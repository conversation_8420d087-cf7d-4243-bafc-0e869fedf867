package com.hean.nucleus.domain.support.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * 短信记录
 */
@Data
@TableName("t_sms")
public class TSms {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String mobile;

    private String message;

    private LocalDateTime sendTime;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 删除标记：1=已删除，0=未删除
     */
    private Integer deleteFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

    private String itemHash;

    public String joinItemHash() {
        return (StringUtils.isBlank(mobile) ? "null" : mobile) + "," + (StringUtils.isBlank(message) ? "null" : message);
    }

}
