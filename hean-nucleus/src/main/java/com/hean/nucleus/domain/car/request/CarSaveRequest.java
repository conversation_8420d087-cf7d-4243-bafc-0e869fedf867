package com.hean.nucleus.domain.car.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

//车辆添加请求
@Data
public class CarSaveRequest {

    //车辆id，修改时必传
    private Long id;

    //所属分组
    @NotNull(message = "所属分组不能为空")
    private Long carGroupId;

    //车牌号
    @NotBlank(message = "车牌号不能为空")
    private String carNumber;

//    /**
//     * 萤石云NVR设备序列号
//     */
//    @NotBlank(message = "萤石云NVR设备序列号不能为空")
//    private String ys7DeviceSerial;
//
//    //NVR设备ID
//    @NotBlank(message = "NVR设备ID不能为空")
//    private String nvrDeviceId;
//
//    //司机
//    private String driver;
//
//    //防控绑定状态，0=未绑定/1=绑定
//    @NotNull(message = "防控绑定状态不能为空")
//    @Range(min = 0, max = 1, message = "防控绑定状态无效")
//    private Integer protectBindFlag;
//
//    /**
//     * 门磁名称，protectBindFlag=1时必传
//     */
//    private String gateName;
//
//    /**
//     * 门磁indexCode，protectBindFlag=1时必传
//     */
//    private String gateIndexCode;
//
//    /**
//     * 报警器名称，protectBindFlag=1时必传
//     */
//    private String alarmName;
//
//    /**
//     * 报警器名称，protectBindFlag=1时必传
//     */
//    private String alarmIndexCode;
}
