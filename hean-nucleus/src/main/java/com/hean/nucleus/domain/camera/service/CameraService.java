package com.hean.nucleus.domain.camera.service;

import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.camera.mapper.TCameraMapper;
import com.hean.nucleus.domain.camera.model.TCamera;
import com.hean.nucleus.domain.camera.request.CameraAddRequest;
import com.hean.nucleus.domain.camera.response.PlayResponse;
import com.hean.nucleus.domain.car.mapper.TCarMapper;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.recorder.mapper.TRecorderMapper;
import com.hean.nucleus.domain.recorder.model.TRecorder;
import com.hean.nucleus.domain.ys7.request.Ys7CameraPreviewUrlRequest;
import com.hean.nucleus.domain.ys7.service.Ys7Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@Service
public class CameraService {

    @Resource
    private TCarMapper carMapper;

    @Resource
    private TRecorderMapper recorderMapper;

    @Resource
    private TCameraMapper cameraMapper;

    @Resource
    private Ys7Service ys7Service;

    public void add(CameraAddRequest request) {
        TCar car = carMapper.selectById(request.getCarId());
        if (car == null || car.getDeleteFlag() == 1) {
            throw new ServiceException("车辆不存在");
        }

        TRecorder recorder = recorderMapper.selectOne(s -> s
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getCarBoundFlag, 1)
                .eq(TRecorder::getCarId, request.getCarId())
        );
        if(recorder == null) {
            throw new ServiceException("车辆还未绑定NVR设备");
        }

        if(cameraMapper.exists(s -> s
                .eq(TCamera::getDeleteFlag, 0)
                .eq(TCamera::getRecorderBoundFlag, 1)
                .eq(TCamera::getRecorderId, recorder.getId())
                .eq(TCamera::getYs7ChannelNo, request.getYs7ChannelNo())
        )) {
            throw new ServiceException("车辆已存在相同摄像头通道序号");
        }

        if(cameraMapper.exists(s -> s
                .eq(TCamera::getDeleteFlag, 0)
                .eq(TCamera::getRecorderBoundFlag, 1)
                .eq(TCamera::getRecorderId, recorder.getId())
                .eq(TCamera::getYs7ChannelName, request.getYs7ChannelName())
        )) {
            throw new ServiceException("车辆已存在相同摄像头通道名称");
        }

        if(cameraMapper.exists(s -> s
                .eq(TCamera::getDeleteFlag, 0)
                .eq(TCamera::getRecorderBoundFlag, 1)
                .eq(TCamera::getRecorderId, recorder.getId())
                .eq(TCamera::getHkIndexCode, request.getHkIndexCode())
        )) {
            throw new ServiceException("车辆已存在相同摄像头indexCode");
        }

        TCamera cameraModel = new TCamera();
        cameraModel.setRecorderBoundFlag(1);
        cameraModel.setRecorderId(recorder.getId());
        cameraModel.setCarId(car.getId());
        cameraModel.setYs7ChannelNo(request.getYs7ChannelNo());
        cameraModel.setYs7ChannelName(request.getYs7ChannelName());
        cameraModel.setHkIndexCode(request.getHkIndexCode());
        cameraModel.setCreateTime(LocalDateTime.now());
        cameraModel.setUpdateTime(LocalDateTime.now());
        cameraModel.setDeleteFlag(0);
        cameraMapper.insert(cameraModel);
    }

    public void delete(IdRequest request) {
        TCamera cameraModel = cameraMapper.selectById(request.getId());
        if (cameraModel == null || cameraModel.getDeleteFlag() == 1) {
            throw new RuntimeException("摄像头不存在");
        }

        cameraModel.setDeleteFlag(1);
        cameraModel.setDeleteTime(LocalDateTime.now());
        cameraMapper.updateById(cameraModel);
    }

    public PlayResponse getPlayUrl(Ys7CameraPreviewUrlRequest request) {
        TCamera cameraModel = cameraMapper.selectById(request.getCameraId());
        if (cameraModel == null || cameraModel.getDeleteFlag() == 1) {
            throw new RuntimeException("摄像头不存在");
        }

        TRecorder recorder = recorderMapper.selectById(cameraModel.getRecorderId());
        if(recorder == null || recorder.getDeleteFlag() == 1) {
            throw new RuntimeException("NVR设备不存在");
        }

        return ys7Service.getCameraUrl(recorder.getYs7DeviceSerial(), cameraModel.getYs7ChannelNo(), request);
    }

}
