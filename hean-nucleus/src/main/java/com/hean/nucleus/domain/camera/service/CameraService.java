package com.hean.nucleus.domain.camera.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.camera.mapper.TCameraMapper;
import com.hean.nucleus.domain.camera.model.TCamera;
import com.hean.nucleus.domain.camera.request.CameraAddRequest;
import com.hean.nucleus.domain.camera.request.CameraListRequest;
import com.hean.nucleus.domain.camera.request.CameraUpdateRequest;
import com.hean.nucleus.domain.camera.response.CameraDetailResponse;
import com.hean.nucleus.domain.camera.response.CameraListResponse;
import com.hean.nucleus.domain.camera.response.PlayResponse;
import com.hean.nucleus.domain.car.mapper.TCarGroupMapper;
import com.hean.nucleus.domain.car.mapper.TCarMapper;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.car.model.TCarGroup;
import com.hean.nucleus.domain.recorder.mapper.TRecorderMapper;
import com.hean.nucleus.domain.recorder.model.TRecorder;
import com.hean.nucleus.domain.ys7.request.Ys7CameraPreviewUrlRequest;
import com.hean.nucleus.domain.ys7.service.Ys7Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CameraService {

    @Resource
    private TCarMapper carMapper;

    @Resource
    private TCarGroupMapper carGroupMapper;

    @Resource
    private TRecorderMapper recorderMapper;

    @Resource
    private TCameraMapper cameraMapper;

    @Resource
    private Ys7Service ys7Service;

    public void add(CameraAddRequest request) {
        TCar car = carMapper.selectById(request.getCarId());
        if (car == null || car.getDeleteFlag() == 1) {
            throw new ServiceException("车辆不存在");
        }

        TRecorder recorder = recorderMapper.selectOne(s -> s
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getCarBoundFlag, 1)
                .eq(TRecorder::getCarId, request.getCarId())
        );
        if(recorder == null) {
            throw new ServiceException("车辆还未绑定NVR设备");
        }

        if(cameraMapper.exists(s -> s
                .eq(TCamera::getDeleteFlag, 0)
                .eq(TCamera::getRecorderBoundFlag, 1)
                .eq(TCamera::getRecorderId, recorder.getId())
                .eq(TCamera::getYs7ChannelNo, request.getYs7ChannelNo())
        )) {
            throw new ServiceException("车辆已存在相同摄像头通道序号");
        }

        if(cameraMapper.exists(s -> s
                .eq(TCamera::getDeleteFlag, 0)
                .eq(TCamera::getRecorderBoundFlag, 1)
                .eq(TCamera::getRecorderId, recorder.getId())
                .eq(TCamera::getYs7ChannelName, request.getYs7ChannelName())
        )) {
            throw new ServiceException("车辆已存在相同摄像头通道名称");
        }

        if(cameraMapper.exists(s -> s
                .eq(TCamera::getDeleteFlag, 0)
                .eq(TCamera::getRecorderBoundFlag, 1)
                .eq(TCamera::getRecorderId, recorder.getId())
                .eq(TCamera::getHkIndexCode, request.getHkIndexCode())
        )) {
            throw new ServiceException("车辆已存在相同摄像头indexCode");
        }

        TCamera cameraModel = new TCamera();
        cameraModel.setRecorderBoundFlag(1);
        cameraModel.setRecorderId(recorder.getId());
        cameraModel.setCarId(car.getId());
        cameraModel.setYs7ChannelNo(request.getYs7ChannelNo());
        cameraModel.setYs7ChannelName(request.getYs7ChannelName());
        cameraModel.setHkIndexCode(request.getHkIndexCode());
        cameraModel.setCreateTime(LocalDateTime.now());
        cameraModel.setUpdateTime(LocalDateTime.now());
        cameraModel.setDeleteFlag(0);
        cameraMapper.insert(cameraModel);
    }

    public void delete(IdRequest request) {
        TCamera cameraModel = cameraMapper.selectById(request.getId());
        if (cameraModel == null || cameraModel.getDeleteFlag() == 1) {
            throw new RuntimeException("摄像头不存在");
        }

        cameraModel.setDeleteFlag(1);
        cameraModel.setDeleteTime(LocalDateTime.now());
        cameraMapper.updateById(cameraModel);
    }

    public PlayResponse getPlayUrl(Ys7CameraPreviewUrlRequest request) {
        TCamera cameraModel = cameraMapper.selectById(request.getCameraId());
        if (cameraModel == null || cameraModel.getDeleteFlag() == 1) {
            throw new RuntimeException("摄像头不存在");
        }

        TRecorder recorder = recorderMapper.selectById(cameraModel.getRecorderId());
        if(recorder == null || recorder.getDeleteFlag() == 1) {
            throw new RuntimeException("NVR设备不存在");
        }

        return ys7Service.getCameraUrl(recorder.getYs7DeviceSerial(), cameraModel.getYs7ChannelNo(), request);
    }

    /**
     * 更新摄像头
     */
    public void update(CameraUpdateRequest request) {
        TCamera cameraModel = cameraMapper.selectById(request.getId());
        if (cameraModel == null || cameraModel.getDeleteFlag() == 1) {
            throw new ServiceException("摄像头不存在");
        }

        // 如果更新了通道序号，需要检查是否重复
        if (request.getYs7ChannelNo() != null && !request.getYs7ChannelNo().equals(cameraModel.getYs7ChannelNo())) {
            if(cameraMapper.exists(s -> s
                    .eq(TCamera::getDeleteFlag, 0)
                    .eq(TCamera::getRecorderBoundFlag, 1)
                    .eq(TCamera::getRecorderId, cameraModel.getRecorderId())
                    .eq(TCamera::getYs7ChannelNo, request.getYs7ChannelNo())
                    .ne(TCamera::getId, request.getId())
            )) {
                throw new ServiceException("车辆已存在相同摄像头通道序号");
            }
        }

        // 如果更新了通道名称，需要检查是否重复
        if (StringUtils.hasText(request.getYs7ChannelName()) && !request.getYs7ChannelName().equals(cameraModel.getYs7ChannelName())) {
            if(cameraMapper.exists(s -> s
                    .eq(TCamera::getDeleteFlag, 0)
                    .eq(TCamera::getRecorderBoundFlag, 1)
                    .eq(TCamera::getRecorderId, cameraModel.getRecorderId())
                    .eq(TCamera::getYs7ChannelName, request.getYs7ChannelName())
                    .ne(TCamera::getId, request.getId())
            )) {
                throw new ServiceException("车辆已存在相同摄像头通道名称");
            }
        }

        // 如果更新了indexCode，需要检查是否重复
        if (StringUtils.hasText(request.getHkIndexCode()) && !request.getHkIndexCode().equals(cameraModel.getHkIndexCode())) {
            if(cameraMapper.exists(s -> s
                    .eq(TCamera::getDeleteFlag, 0)
                    .eq(TCamera::getRecorderBoundFlag, 1)
                    .eq(TCamera::getRecorderId, cameraModel.getRecorderId())
                    .eq(TCamera::getHkIndexCode, request.getHkIndexCode())
                    .ne(TCamera::getId, request.getId())
            )) {
                throw new ServiceException("车辆已存在相同摄像头indexCode");
            }
        }

        // 更新字段
        if (StringUtils.hasText(request.getName())) {
            cameraModel.setName(request.getName());
        }
        if (request.getYs7ChannelNo() != null) {
            cameraModel.setYs7ChannelNo(request.getYs7ChannelNo());
        }
        if (StringUtils.hasText(request.getYs7ChannelName())) {
            cameraModel.setYs7ChannelName(request.getYs7ChannelName());
        }
        if (StringUtils.hasText(request.getHkIndexCode())) {
            cameraModel.setHkIndexCode(request.getHkIndexCode());
        }
        cameraModel.setUpdateTime(LocalDateTime.now());

        cameraMapper.updateById(cameraModel);
    }

    /**
     * 摄像头分页列表查询
     */
    public PageView<CameraListResponse> list(CameraListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        List<TCamera> cameras = cameraMapper.selectList(wrapper -> {
            wrapper.eq(TCamera::getDeleteFlag, 0);

            if (StringUtils.hasText(request.getName())) {
                wrapper.like(TCamera::getName, request.getName());
            }
            if (request.getCarId() != null) {
                wrapper.eq(TCamera::getCarId, request.getCarId());
            }
            if (request.getRecorderId() != null) {
                wrapper.eq(TCamera::getRecorderId, request.getRecorderId());
            }
            if (request.getRecorderBoundFlag() != null) {
                wrapper.eq(TCamera::getRecorderBoundFlag, request.getRecorderBoundFlag());
            }
            if (request.getYs7ChannelNo() != null) {
                wrapper.eq(TCamera::getYs7ChannelNo, request.getYs7ChannelNo());
            }
            if (StringUtils.hasText(request.getYs7ChannelName())) {
                wrapper.like(TCamera::getYs7ChannelName, request.getYs7ChannelName());
            }
            if (StringUtils.hasText(request.getHkIndexCode())) {
                wrapper.like(TCamera::getHkIndexCode, request.getHkIndexCode());
            }

            wrapper.orderByDesc(TCamera::getCreateTime);

            return wrapper;
        });

        PageInfo<TCamera> pageInfo = new PageInfo<>(cameras);

        List<CameraListResponse> responseList = cameras.stream().map(camera -> {
            CameraListResponse response = new CameraListResponse();
            BeanUtils.copyProperties(camera, response);

            // 获取车辆信息
            if (camera.getCarId() != null) {
                TCar car = carMapper.selectById(camera.getCarId());
                if (car != null && car.getDeleteFlag() == 0) {
                    response.setCarNumber(car.getCarNumber());
                }
            }

            // 获取录像机信息
            if (camera.getRecorderId() != null) {
                TRecorder recorder = recorderMapper.selectById(camera.getRecorderId());
                if (recorder != null && recorder.getDeleteFlag() == 0) {
                    response.setRecorderSerial(recorder.getYs7DeviceSerial());
                    // 这里可以设置录像机名称，如果有的话
                    response.setRecorderName("NVR-" + recorder.getYs7DeviceSerial());
                }
            }

            // 设置在线状态，这里默认为0，实际项目中可能需要调用萤石云API获取真实状态
            response.setStatus(0);

            return response;
        }).collect(Collectors.toList());

        return PageView.from(pageInfo, responseList);
    }

    /**
     * 摄像头详情查询
     */
    public CameraDetailResponse detail(IdRequest request) {
        TCamera cameraModel = cameraMapper.selectById(request.getId());
        if (cameraModel == null || cameraModel.getDeleteFlag() == 1) {
            throw new ServiceException("摄像头不存在");
        }

        CameraDetailResponse response = new CameraDetailResponse();
        BeanUtils.copyProperties(cameraModel, response);

        // 获取车辆信息
        if (cameraModel.getCarId() != null) {
            TCar car = carMapper.selectById(cameraModel.getCarId());
            if (car != null && car.getDeleteFlag() == 0) {
                response.setCarNumber(car.getCarNumber());

                // 获取车辆分组信息
                if (car.getCarGroupId() != null) {
                    TCarGroup carGroup = carGroupMapper.selectById(car.getCarGroupId());
                    if (carGroup != null && carGroup.getDeleteFlag() == 0) {
                        response.setCarGroupName(carGroup.getName());
                    }
                }
            }
        }

        // 获取录像机信息
        if (cameraModel.getRecorderId() != null) {
            TRecorder recorder = recorderMapper.selectById(cameraModel.getRecorderId());
            if (recorder != null && recorder.getDeleteFlag() == 0) {
                response.setRecorderSerial(recorder.getYs7DeviceSerial());
                response.setYs7DeviceSerial(recorder.getYs7DeviceSerial());
                // 这里可以设置录像机名称，如果有的话
                response.setRecorderName("NVR-" + recorder.getYs7DeviceSerial());
            }
        }

        // 设置在线状态，这里默认为0，实际项目中可能需要调用萤石云API获取真实状态
        response.setStatus(0);

        return response;
    }

}
