package com.hean.nucleus.domain.camera.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * <p>
 * 摄像头
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_camera")
public class TCamera {

    /**
     * 摄像头ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 摄像头名称
     */
    private String name;

    /**
     * 是否已绑定到NVR，0:未绑定，1:已绑定
     */
    private Integer recorderBoundFlag;

    /**
     * 绑定的设备ID
     */
    private Long recorderId;

    /**
     * 车辆ID，冗余RECORDER中的CAR_ID
     */
    private Long carId;

    /**
     * 萤石云摄像头通道序号
     */
    private Integer ys7ChannelNo;

    /**
     * 萤石云摄像头通道名称
     */
    private String ys7ChannelName;

    /**
     * 海康摄像头indexCode
     */
    private String hkIndexCode;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记，1:已删除，0:未删除
     */
    private Integer deleteFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

    /**
     * 后台删除操作用户
     */
    private Long deleteSysUserId;


}
