package com.hean.nucleus.domain.recorder.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.bean.Recorder;
import com.hean.nucleus.domain.recorder.request.*;
import com.hean.nucleus.domain.recorder.mapper.RadpacDevInfoMapper;
import com.hean.nucleus.domain.recorder.mapper.TRecorderMapper;
import com.hean.nucleus.domain.recorder.model.RadpacDevInfo;
import com.hean.nucleus.domain.recorder.model.TRecorder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * RadpacDevInfo表中的数据相当于NVR设备资源库，表中的数据需要先通过关联添加的方式加入到主表TRecorder中才能使用
 */
@Slf4j
@Service
public class RecorderService {

    @Resource
    private TRecorderMapper recorderMapper;

    @Resource
    private RadpacDevInfoMapper radpacDevInfoMapper;

    public List<RadpacDevInfo> listRadpacRecorders(RadpacRecorderListRequest request) {
        return radpacDevInfoMapper.list(request);
    }

    /**
     * 把 radpac 表中的设备添加到主体表
     */
    public void add(RecorderAddRequest request) {
        // 先判断RadpacDevInfo表中的记录是否存在
        RadpacDevInfo radpacDevInfo = radpacDevInfoMapper.selectById(request.getRadpacRecorderId());
        if(radpacDevInfo == null) {
            throw new ServiceException("radpac设备不存在");
        }

        TRecorder recorder = recorderMapper.selectOne(s -> s
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getRadpacRecorderId, request.getRadpacRecorderId())
        );
        if(recorder != null) {
            throw new ServiceException("设备已关联");
        }

        // 扩展表中还没有数据
        recorder = new TRecorder();
        recorder.setRadpacRecorderId(request.getRadpacRecorderId());
        recorder.setYs7DeviceSerial(request.getYs7DeviceSerial());
        recorder.setCreateTime(LocalDateTime.now());
        recorder.setUpdateTime(LocalDateTime.now());
        recorder.setDeleteFlag(0);
        recorderMapper.insert(recorder);
    }


    @Transactional
    public void update(RecorderUpdateRequest request) {
        TRecorder recorder = recorderMapper.selectById(request.getId());
        if(recorder == null || recorder.getDeleteFlag() == 1) {
            throw new ServiceException("设备不存在");
        }

        RadpacDevInfo radpacDevInfo = radpacDevInfoMapper.selectById(request.getRadpacRecorderId());
        if(radpacDevInfo == null) {
            throw new ServiceException("radpac设备不存在");
        }

        recorder.setRadpacRecorderId(request.getRadpacRecorderId());
        recorder.setYs7DeviceSerial(request.getYs7DeviceSerial());
        recorder.setUpdateTime(LocalDateTime.now());
        recorderMapper.updateById(recorder);
    }

    @Transactional
    public void bindCar(RecorderBindRequest request) {
        // 判断设备是否存在
        TRecorder recorder = recorderMapper.selectById(request.getRecorderId());
        if(recorder == null || recorder.getDeleteFlag() == 1) {
            throw new ServiceException("设备不存在");
        }

        RadpacDevInfo radpacDevInfo = radpacDevInfoMapper.selectById(recorder.getRadpacRecorderId());
        if(radpacDevInfo == null) {
            throw new ServiceException("radpac设备不存在");
        }

        if(recorder.getCarBoundFlag() == 1) {
            throw new ServiceException("设备已绑定车辆");
        }

        // 判断车辆是否已绑定设备
        if(recorderMapper.exists(s -> s
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getCarBoundFlag, 1)
                .eq(TRecorder::getCarId, request.getCarId())
        )) {
            throw new RuntimeException("车辆已绑定设备");
        }

        // 绑定车辆
        recorder.setCarBoundFlag(1);
        recorder.setCarId(request.getCarId());
        recorder.setUpdateTime(LocalDateTime.now());
        recorderMapper.updateById(recorder);

    }

    @Transactional
    public void unbindCar(IdRequest request) {
        TRecorder recorder = recorderMapper.selectById(request.getId());
        if(recorder == null || recorder.getDeleteFlag() == 1) {
            throw new ServiceException("设备不存在");
        }

        recorder.setCarBoundFlag(0);
        recorder.setCarId(null);
        recorder.setUpdateTime(LocalDateTime.now());
        recorderMapper.updateById(recorder);
    }

    /**
     * 删除设备，逻辑删除
     * @param request
     */
    @Transactional
    public void delete(IdRequest request) {
        TRecorder recorder = recorderMapper.selectById(request.getId());
        if(recorder == null || recorder.getDeleteFlag() == 1) {
            throw new ServiceException("设备不存在");
        }

        recorder.setDeleteFlag(1);
        recorder.setDeleteTime(LocalDateTime.now());
        recorderMapper.updateById(recorder);
    }

    public Recorder queryByCarId(Long carId) {
        TRecorder dbRecorder = recorderMapper.selectOne(s -> s
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getCarBoundFlag, 1)
                .eq(TRecorder::getCarId, carId)
        );

        if(dbRecorder == null) {
            return null;
        }

        RadpacDevInfo radpacDevInfo = radpacDevInfoMapper.selectById(dbRecorder.getRadpacRecorderId());
        if(radpacDevInfo == null) {
            return null;
        }

        Recorder recorder = new Recorder();
        BeanUtils.copyProperties(radpacDevInfo, recorder);
        BeanUtils.copyProperties(dbRecorder, recorder);

        return recorder;
    }

    public PageView<Recorder> listPage(RecorderListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        PageInfo<Recorder> pageInfo = new PageInfo<>(recorderMapper.list(request));
        return PageView.from(pageInfo, pageInfo.getList());
    }


    // 详情
    public Recorder detail(IdRequest request) {
        TRecorder dbRecorder = recorderMapper.selectById(request.getId());
        if(dbRecorder == null || dbRecorder.getDeleteFlag() == 1) {
            throw new ServiceException("设备不存在");
        }

        RadpacDevInfo radpacDevInfo = radpacDevInfoMapper.selectById(dbRecorder.getRadpacRecorderId());
        if(radpacDevInfo == null) {
            throw new ServiceException("radpac设备不存在");
        }

        Recorder recorder = new Recorder();
        BeanUtils.copyProperties(dbRecorder, recorder);
        BeanUtils.copyProperties(radpacDevInfo, recorder);

        return recorder;
    }

    /**
     * 修改布防状态
     * @param request
     */
    public void changeDefense(DefenseChangeRequest request) {
        RadpacDevInfo radpacDevInfo = radpacDevInfoMapper.selectById(request.getRadpacRecorderId());
        if(radpacDevInfo == null) {
            throw new RuntimeException("设备不存在");
        }

        radpacDevInfo.setDefenseSet(request.getDefenseSet());
        radpacDevInfoMapper.updateById(radpacDevInfo);
    }

}
