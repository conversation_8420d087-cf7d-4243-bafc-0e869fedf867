package com.hean.nucleus.domain.recorder.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * radpac海康NVR设备日志记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("radpac_dev_log")
public class RadpacDevLog {

    /**
     * 日志记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备时间
     */
    private LocalDateTime logTime;

    /**
     * 日志主类型
     */
    private Integer majorType;

    /**
     * 日志次类型
     */
    private String minorType;

    /**
     * radpac_dev_info表设备ID
     */
    @TableField(value = "device_id")
    private String radpacRecorderId;

}
