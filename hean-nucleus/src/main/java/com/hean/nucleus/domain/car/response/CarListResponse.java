package com.hean.nucleus.domain.car.response;

import com.hean.common.annotation.FieldDoc;
import com.hean.nucleus.domain.car.model.TCar;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarListResponse extends TCar {

    //1:分组,2:车辆,3:探测器,4:摄像头
    @FieldDoc(friendName = "数据类型", required = true, description = "数据类型(1:分组/2:车辆/3:探测器/4:摄像头)")
    private Integer dataType = 2;

    @FieldDoc(friendName = "所属分组名称", required = true, description = "所属分组名称")
    private String carGroupName;


}
