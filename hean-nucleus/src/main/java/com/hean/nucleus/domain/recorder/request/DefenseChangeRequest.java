package com.hean.nucleus.domain.recorder.request;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

@Data
public class DefenseChangeRequest {

    /**
     * radpac设备ID
     */
    @NotBlank(message = "设备id不能为空")
    private String radpacRecorderId;

    /**
     * 布防设置，0：撤防、1：布防
     */
    @NotBlank(message = "布防设置不能为空")
    @Range(min = 0, max = 1, message = "布防设置无效")
    private Integer defenseSet;

}
