package com.hean.nucleus.domain.recorder.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 海康NVR设备修改请求
 */
@Data
public class RecorderUpdateRequest {

    private Long id;

    /**
     * radpac设备ID
     */
    @NotBlank(message = "设备ID不能为空")
    private String radpacRecorderId;

    @NotBlank(message = "设备名称不能为空")
    private String name;

    @NotBlank(message = "萤石云设备序列号不能为空")
    private String ys7DeviceSerial;

}
