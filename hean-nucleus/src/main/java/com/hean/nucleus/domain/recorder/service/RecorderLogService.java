package com.hean.nucleus.domain.recorder.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hean.common.annotation.MethodDoc;
import com.hean.common.exception.ServiceException;
import com.hean.nucleus.common.database.PageView;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.mapper.TCarMapper;
import com.hean.nucleus.domain.car.model.TCar;
import com.hean.nucleus.domain.recorder.mapper.RadpacDevLogDetialMapper;
import com.hean.nucleus.domain.recorder.mapper.RadpacDevLogMapper;
import com.hean.nucleus.domain.recorder.mapper.TRecorderMapper;
import com.hean.nucleus.domain.recorder.model.RadpacDevLog;
import com.hean.nucleus.domain.recorder.model.RadpacDevLogDetial;
import com.hean.nucleus.domain.recorder.model.TRecorder;
import com.hean.nucleus.domain.recorder.request.RecorderLogListRequest;
import com.hean.nucleus.domain.recorder.response.RecorderLogResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class RecorderLogService {

    @Resource
    private RadpacDevLogMapper radpacDevLogMapper;

    @Resource
    private RadpacDevLogDetialMapper radpacDevLogDetialMapper;

    @Resource
    private TRecorderMapper recorderMapper;

    @Resource
    private TCarMapper carMapper;

    /**
     * 分页查询设备日志
     *
     * @param request 查询条件
     * @return 设备日志列表
     */
    @MethodDoc(name = "广东省城市放射源收贮海康NVR设备日志信息")
    public PageView<RecorderLogResponse> listPage(RecorderLogListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        PageInfo<RecorderLogResponse> pageInfo = new PageInfo<>(radpacDevLogMapper.list(request));
        
        return PageView.from(pageInfo, pageInfo.getList());
    }

    /**
     * 查询设备日志详情
     *
     * @param request 日志ID
     * @return 日志详情
     */
    public RecorderLogResponse detail(IdRequest request) {
        RadpacDevLog devLog = radpacDevLogMapper.selectById(request.getId());
        if (devLog == null) {
            throw new ServiceException("设备日志不存在");
        }

        RecorderLogResponse response = new RecorderLogResponse();
        BeanUtils.copyProperties(devLog, response);

        // 获取车辆信息
        TRecorder recorder = recorderMapper.selectOne(r -> r
                .eq(TRecorder::getDeleteFlag, 0)
                .eq(TRecorder::getRadpacRecorderId, devLog.getRadpacRecorderId())
        );
        
        if (recorder != null) {
            TCar car = carMapper.selectById(recorder.getCarId());
            if (car != null && car.getDeleteFlag() == 0) {
                response.setCarId(car.getId());
                response.setCarNumber(car.getCarNumber());
            }
        }

        // 获取日志描述信息
        RadpacDevLogDetial logDetial = radpacDevLogDetialMapper.selectOne(d -> d
                .eq(RadpacDevLogDetial::getMajorType, devLog.getMajorType())
                .eq(RadpacDevLogDetial::getValue, devLog.getMinorType())
        );
        
        if (logDetial != null) {
            response.setDescription(logDetial.getDescribe());
        }

        return response;
    }

}
