package com.hean.nucleus.domain.support.mapper;

import com.hean.common.annotation.DataSource;
import com.hean.common.enums.DataSourceType;
import com.hean.nucleus.common.database.CoreMapper;
import com.hean.nucleus.domain.support.model.TSms;
import com.hean.nucleus.domain.support.request.SmsListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 短信记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@DataSource(DataSourceType.MASTER)
public interface TSmsMapper extends CoreMapper<TSms> {

    /**
     * 分页查询短信记录列表
     *
     * @param request 查询条件
     * @return 短信记录列表
     */
    List<TSms> selectSmsRecordList(@Param("request") SmsListRequest request);

    /**
     * 查询短信记录详情
     *
     * @param id 短信记录ID
     * @return 短信记录详情
     */
    TSms selectSmsRecordDetail(@Param("id") Long id);
}
