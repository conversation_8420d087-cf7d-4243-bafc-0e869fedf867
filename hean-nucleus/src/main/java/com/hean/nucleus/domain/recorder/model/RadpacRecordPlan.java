package com.hean.nucleus.domain.recorder.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * radpac海康NVR录像计划设置
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("radpac_record_plan")
public class RadpacRecordPlan {

    /**
     * 录像计划ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 通道号(两个摄像头，1和2)
     */
    private Integer channelNum;


    /**
     * 星期一到日(0-6)
     */
    private Integer weekNum;

    /**
     * 是否启用录像(0、1)
     */
    private Integer isUse;

    /**
     * 延录时间
     */
    private Integer recordTime;

    /**
     * 预录时间
     */
    private Integer preRecordTime;

    /**
     * 是否全天录像，0否，1是
     */
    private Integer isAllday;

    /**
     * 录像触发类型
     */
    private Integer recordType;

    /**
     * 开始时间1
     */
    private String startTime1;

    /**
     * 结束时间1
     */
    private String stopTime1;

    /**
     * 开始时间2
     */
    private String startTime2;

    /**
     * 结束时间2
     */
    private String stopTime2;

    /**
     * 是否下发（1需要下发，0已下发）
     */
    private Integer downLoad;

    /**
     * radpac_dev_info表设备ID
     */
    private String deviceId;

}
