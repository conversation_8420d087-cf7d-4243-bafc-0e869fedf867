package com.hean.nucleus.domain.recorder.request;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 海康NVR设备日志分页查询请求
 */
@Data
public class RadpacDevLogListRequest {

    /**
     * 当前记录起始索引
     */
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    private Integer pageSize;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 日志主类型
     */
    private Integer majorType;

    /**
     * 日志次类型
     */
    private String minorType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
