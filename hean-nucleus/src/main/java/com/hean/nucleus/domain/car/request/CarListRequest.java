package com.hean.nucleus.domain.car.request;

import com.hean.common.annotation.FieldDoc;
import lombok.Data;

@Data
public class CarListRequest {

    /** 当前记录起始索引 */
    @FieldDoc(friendName = "页码", required = true, description = "分页页码")
    private Integer pageNum;

    /** 每页显示记录数 */
    @FieldDoc(friendName = "分页大小", required = true, description = "分页大小")
    private Integer pageSize;

    //所属分组
    @FieldDoc(friendName = "所属分组", required = false, description = "所属分组")
    private Long carGroupId;

    //车牌号
    @FieldDoc(friendName = "车牌号", required = false, description = "车牌号")
    private String carNumber;

}
