package com.hean.nucleus.domain.detector.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Min;
import java.util.Date;

@Data
public class DetectorDataListRequest {

    /** 当前记录起始索引 */
    private Integer pageNum;

    /** 每页显示记录数 */
    private Integer pageSize;

    @Min(value = 1, message = "探测器id无效")
    private Long id;

    @Min(value = 1, message = "radpac探测器id无效")
    private Long radpacDetectorId;

    // 主机通讯编号
    private String commId;

    // 主机序列号
    private String monitorSn;

    private String sensorId;

    // 探测器序列号
    private String sensorSn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
