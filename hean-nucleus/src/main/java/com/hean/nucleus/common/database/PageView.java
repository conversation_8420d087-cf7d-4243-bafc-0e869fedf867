package com.hean.nucleus.common.database;

import com.github.pagehelper.PageInfo;
import com.hean.common.annotation.FieldDoc;
import lombok.Data;

import java.util.List;

@Data
public class PageView<T> {

    @FieldDoc(friendName = "当前页码", required = true, description = "当前页码")
    private int pageNum;

    @FieldDoc(friendName = "每页大小", required = true, description = "每页大小")
    private int pageSize;

    @FieldDoc(friendName = "当前页数据大小", required = true, description = "当前页数据大小")
    private int size;

    @FieldDoc(friendName = "总数据量", required = true, description = "总数据量")
    private long total;

    @FieldDoc(friendName = "起始行", required = true, description = "起始行")
    private long startRow;

    @FieldDoc(friendName = "结束行", required = true, description = "结束行")
    private long endRow;

    @FieldDoc(friendName = "总页数", required = true, description = "总页数")
    private int pages;

    @FieldDoc(friendName = "上一页", required = true, description = "上一页")
    private int prePage;

    @FieldDoc(friendName = "下一页", required = true, description = "下一页")
    private int nextPage;

    @FieldDoc(friendName = "是否第一页", required = true, description = "是否第一页")
    private boolean isFirstPage;

    @FieldDoc(friendName = "是否最后一页", required = true, description = "是否最后一页")
    private boolean isLastPage;

    @FieldDoc(friendName = "是否有上一页", required = true, description = "是否有上一页")
    private boolean hasPreviousPage;

    @FieldDoc(friendName = "是否有下一页", required = true, description = "是否有下一页")
    private boolean hasNextPage;

    @FieldDoc(friendName = "列表数据", required = true, description = "列表数据")
    private List<T> list;

    public static <M, T> PageView<T> from(PageInfo<M> info, List<T> list) {
        PageView<T> pageView = new PageView<>();

        pageView.setPageNum(info.getPageNum());
        pageView.setPageSize(info.getPageSize());
        pageView.setSize(info.getSize());
        pageView.setStartRow(info.getStartRow());
        pageView.setEndRow(info.getEndRow());
        pageView.setPages(info.getPages());
        pageView.setPrePage(info.getPrePage());
        pageView.setNextPage(info.getNextPage());
        pageView.setFirstPage(info.isIsFirstPage());
        pageView.setLastPage(info.isIsLastPage());
        pageView.setHasPreviousPage(info.isHasPreviousPage());
        pageView.setHasNextPage(info.isHasNextPage());
        pageView.setTotal(info.getTotal());

        pageView.setList(list);

        return pageView;
    }

    public static <T> PageView<T> empty() {
        PageView<T> pageView = new PageView<>();

        pageView.setPageNum(0);
        pageView.setPageSize(0);
        pageView.setSize(0);
        pageView.setStartRow(0);
        pageView.setEndRow(0);
        pageView.setPages(0);
        pageView.setPrePage(0);
        pageView.setNextPage(0);
        pageView.setFirstPage(false);
        pageView.setLastPage(false);
        pageView.setHasPreviousPage(false);
        pageView.setHasNextPage(false);
        pageView.setTotal(0L);

        pageView.setList(null);

        return pageView;
    }
}
