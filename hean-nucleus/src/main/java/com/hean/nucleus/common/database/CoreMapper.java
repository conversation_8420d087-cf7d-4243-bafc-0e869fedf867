package com.hean.nucleus.common.database;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.function.Function;

public interface CoreMapper<T> extends BaseMapper<T> {

    default List<T> selectList() {
        return selectList(Wrappers.emptyWrapper());
    }

    default List<T> selectList(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<T>());
        return selectList(wrapper);
    }

    default Long selectCount(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<T>());
        return selectCount(wrapper);
    }

    default boolean exists(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<T>());
        return exists(wrapper);
    }

    default T selectFirst(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
//        List<T> list = selectList(function);
//        if(list == null || list.isEmpty()) {
//            return null;
//        }
//
//        return list.get(0);

        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<T>());
        wrapper.last(" limit 1 ");
        return selectOne(wrapper);
    }

    default T selectOne(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<T>());
        return selectOne(wrapper);
    }

    default int delete(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<T>());
        return delete(wrapper);
    }

    default int update(T entity, Function<LambdaUpdateWrapper<T>, LambdaUpdateWrapper<T>> function) {
        LambdaUpdateWrapper<T> wrapper = function.apply(new LambdaUpdateWrapper<T>());
        return update(entity, wrapper);
    }

    default int update(Function<LambdaUpdateWrapper<T>, LambdaUpdateWrapper<T>> function) {
        LambdaUpdateWrapper<T> wrapper = function.apply(new LambdaUpdateWrapper<T>());
        return update(null, wrapper);
    }
}
