<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hean</artifactId>
        <groupId>com.hean</groupId>
        <version>1.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>hean-nucleus</artifactId>

    <description>
        nucleus
    </description>

    <dependencies>

        <dependency>
            <groupId>com.ezviz.open</groupId>
            <artifactId>ezviz-openplatform</artifactId>
            <version>1.1.6.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.hikvision.ga</groupId>
            <artifactId>artemis-http-client</artifactId>
            <version>1.1.12.RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/artemis-http-client-1.1.12.RELEASE.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.hean</groupId>
            <artifactId>hean-database</artifactId>
        </dependency>

    </dependencies>

</project>