package com.hean.framework.web.service;

import javax.annotation.Resource;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hean.common.core.domain.entity.SysRole;
import com.hean.common.service.CipherService;
import com.hean.common.utils.*;
import com.hean.framework.manager.AsyncManager;
import com.hean.framework.manager.factory.AsyncFactory;
import com.hean.framework.security.context.AuthenticationContextHolder;
import com.hean.system.domain.SysPost;
import com.hean.system.service.ISysPostService;
import com.hean.system.service.ISysRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.hean.common.constant.CacheConstants;
import com.hean.common.constant.Constants;
import com.hean.common.core.domain.entity.SysUser;
import com.hean.common.core.domain.model.LoginUser;
import com.hean.common.core.redis.RedisCache;
import com.hean.common.exception.ServiceException;
import com.hean.common.exception.user.CaptchaException;
import com.hean.common.exception.user.CaptchaExpireException;
import com.hean.common.exception.user.UserPasswordNotMatchException;
import com.hean.common.utils.ip.IpUtils;
import com.hean.system.service.ISysConfigService;
import com.hean.system.service.ISysUserService;

import java.util.Date;
import java.util.Map;

/**
 * 登录校验方法

 */
@Component
@Slf4j
public class SysLoginService
{
    @Resource
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Resource
    private RedisCache redisCache;
    
    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ISysConfigService configService;

    @Resource
    private ISysRoleService sysRoleService;

    @Resource
    private ISysPostService sysPostService;

    @Resource
    private CipherService cipherService;

    @Value("${oauth.prefix}")
    private String oauthPrefix;

    @Value("${oauth.clientId}")
    private String oauthClientId;

    @Value("${oauth.clientSecret}")
    private String oauthClientSecret;

    @Value("${oauth.redirectUri}")
    private String oauthRedirectUri;


    public String getAccessToken(String appId, String appSecret) {

        if (appId == null || appSecret == null) {
            throw new ServiceException("appId或appSecret不能为空");
        }

        if(!appId.equalsIgnoreCase("3916755812066") || !appSecret.equalsIgnoreCase("b46b4c98e0a046708d58419666da7118")) {
            throw new ServiceException("appId或appSecret不正确");
        }

        SysUser sysUser = sysUserService.selectUserById(1L);
        if(sysUser == null) {
            throw new ServiceException("授权失败");
        }

        String username = sysUser.getUserName();
        String password = cipherService.sm4Decrypt(sysUser.getPassword());

        // 用户验证
        Authentication authentication;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage(), e);
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 登录验证
     * 
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {

        boolean captchaEnabled = configService.selectCaptchaEnabled();
        // 验证码开关
        if (captchaEnabled) {
            validateCaptcha(username, code, uuid);
        }

        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage(), e);
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    public JSONObject queryUserData(String code) {
        // 获取 access_token
        String accessToken;
        try {
            String content = HttpUtil.post(oauthPrefix + "/realms/dg/authProxy/oauth/oauth/token", Map.of(
                    "code", code,
                    "client_id", oauthClientId,
                    "client_secret", oauthClientSecret,
                    "grant_type", "authorization_code",
                    "redirect_uri", oauthRedirectUri
            ));
            JSONObject object = JSON.parseObject(content);
            if(object.containsKey("error")) {
                throw new ServiceException(object.toJSONString());
            }
            accessToken = object.getString("access_token");
        } catch (Exception e) {
            log.warn("fetch access token failed by code: {}", code, e);
            throw new ServiceException("获取accessToken失败");
        }

//        System.out.println(accessToken);

        // 获取详细信息
        JSONObject dataJson;
        try {
            String content = HttpUtil.post(oauthPrefix + "/realms/dg/authProxy/oauth/user/detail", Map.of(
                    "access_token", accessToken,
                    "client_id", oauthClientId,
                    "client_secret", oauthClientSecret
            ));
            JSONObject object = JSON.parseObject(content);
            if(object.containsKey("error")) {
                throw new ServiceException(object.toJSONString());
            }
//            System.out.println(object);
            dataJson = object.getJSONObject("data");
        } catch (Exception e) {
            log.warn("fetch user detail failed by accessToken: {}", accessToken, e);
            throw new ServiceException("获取用户详情失败");
        }

        return dataJson;
    }

    public String oauthLogin(String oauthCode) {
        JSONObject dataJson = queryUserData(oauthCode);
        String userCode = dataJson.getString("user_code");
        String username = dataJson.getString("user_name");
        JSONObject userJson = dataJson.getJSONObject("user");

        SysUser sysUser = sysUserService.selectUserByOauthCode(userCode);
        if (sysUser == null) { // 用户不存在需要添加用户
            SysUser existedUser = sysUserService.selectUserByUserName(username);
            if (existedUser != null) {
                throw new ServiceException("用户名已存在，无法自动添加新用户");
            }

            sysUser = new SysUser();
            sysUser.setDeptId(100L);
            sysUser.setUserName(cipherService.sm4Encrypt(username));
            sysUser.setNickName(cipherService.sm4Encrypt(userJson.getString("name")));
            sysUser.setUserSource(1);
            sysUser.setOauthCode(userCode);
            sysUser.setPhonenumber(cipherService.sm4Encrypt(userJson.getString("mobi_tel")));
            sysUser.setPassword(cipherService.sm4Encrypt("<EMAIL>"));
            sysUser.setItemHash(cipherService.calcHash(sysUser.joinItemHash()));
            sysUser.setStatus("0");
            sysUser.setDelFlag("0");
            sysUser.setCreateTime(new Date());
            sysUser.setUpdateTime(new Date());

            SysRole role = sysRoleService.selectRoleById(100L);
            if(role == null) {
                throw new ServiceException("新用户默认角色未找到");
            }
            sysUser.setRoleIds(new Long[]{role.getRoleId()});

            SysPost sysPost = sysPostService.selectPostById(4L);
            if (sysPost == null) {
                throw new ServiceException("新用户默认岗位未找到");
            }
            sysUser.setPostIds(new Long[]{sysPost.getPostId()});

            sysUserService.insertUser(sysUser);
        }

        String password = cipherService.sm4Decrypt(sysUser.getPassword());

        // 用户验证
        Authentication authentication;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     * 
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     */
    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = sysUserService.selectUserById(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUserService.updateUserProfile(sysUser);
    }
}
