# 项目相关配置
hean:
  # 名称
  name: hean
  # 版本
  version: 3.8.4
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/hean/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/hean/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

cipher:
  prefix: http://*************:19108
  clientId: 8hIrGBj07MAMMrUd
  clientSecret: DPTJAbPHbLfwydNH7suNORYg3DKDVY7x
  serverId: auth_center
  applicationId: 6837c292773b6d69d3c2823b
  appSecret: vu9RLoQVawprFCEeZlC4eLrT6e3V1pB7
  sm2KeyId: 1748484739272301
  sm3KeyId: 1748484740681101
  sm4KeyId: 1748484740271301

ys7:
  prefix: https://open.ys7.com/api
  appKey: 7b3a614f80a34b3b93e00c3cb09c4294
  appSecret: 4efabdc8fe17bdaf49457bd7b5459097

oauth:
  prefix: https://www-app.gdeei.cn/sthjzhy2/oauth
  clientId: chezaiyidongjiance
  clientSecret: chezaiyidongjiancefTm8nb6z
  redirectUri: https://dev-hean.js-net.me/auth/sso

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 10098
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
  tongweb:
    license:
      path: classpath:tongweb/license.dat
      type: file

# 日志配置
logging:
  level:
    com.hean: debug
    org.springframework: warn


# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: dm.jdbc.driver.DmDriver
    druid:
      # 主库数据源
      master:
        url: jdbc:dm://*************:17436/nucleus?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=Asia/Shanghai
        username: SYSDBA
        password: heanDb12345
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: hean
        login-password: 123456
      filter:
        slf4j:
          enabled: true
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis 配置
  redis:
    # 地址
    host: r-bp1m2dmndwoc19giwcpd.redis.rds.aliyuncs.com
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 168
    # 密码
    password: r-bp1m2dmndwoc19giwc:Q123456q@
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30
  
# MyBatis配置
mybatis-plus:
    # 搜索指定包别名
    typeAliasesPackage: com.hean.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml
    global-config:
      banner: false

# PageHelper分页插件
pagehelper: 
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql 

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

