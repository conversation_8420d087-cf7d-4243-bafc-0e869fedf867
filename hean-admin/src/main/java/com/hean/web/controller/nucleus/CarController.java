package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.common.core.domain.model.LoginUser;
import com.hean.common.utils.SecurityUtils;
import com.hean.nucleus.domain.car.request.CarListRequest;
import com.hean.nucleus.domain.car.request.CarSaveRequest;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.service.CarService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/car")
public class CarController {

    @Resource
    private CarService carService;

    @PreAuthorize("@ss.hasPermi('nucleus:car:add')")
    @PostMapping("/add")
    public AjaxResult add(
            @Validated @RequestBody CarSaveRequest request
    ) {
        carService.add(request);
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('nucleus:car:update')")
    @PostMapping("/update")
    public AjaxResult update(
            @Validated @RequestBody CarSaveRequest request
    ) {
        carService.update(request);
        return AjaxResult.success();
    }

    @PostMapping("/list")
    public AjaxResult list(
            @Validated @RequestBody CarListRequest request
    ) {
        return AjaxResult.success(carService.list(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:car:listPage')")
    @PostMapping("/listPage")
    public AjaxResult listPage(
            @Validated @RequestBody CarListRequest request
    ) {
        return AjaxResult.success(carService.listPage(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:car:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(
            @Validated @RequestBody IdRequest request
    ) {
        return AjaxResult.success(carService.detail(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:car:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(
            @Validated @RequestBody IdRequest request
    ) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        carService.delete(loginUser, request.getId());
        return AjaxResult.success();
    }

}
