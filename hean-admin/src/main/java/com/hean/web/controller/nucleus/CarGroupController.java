package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.common.core.domain.model.LoginUser;
import com.hean.common.utils.SecurityUtils;
import com.hean.nucleus.domain.car.request.CarGroupListRequest;
import com.hean.nucleus.domain.car.request.CarGroupSaveRequest;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.service.CarGroupService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/carGroup")
public class CarGroupController {

    @Resource
    private CarGroupService carGroupService;

    @PreAuthorize("@ss.hasPermi('nucleus:carGroup:add')")
    @PostMapping("/add")
    public AjaxResult add(
            @Validated @RequestBody CarGroupSaveRequest request
    ) {
        carGroupService.add(request);
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('nucleus:carGroup:update')")
    @PostMapping("/update")
    public AjaxResult update(
            @Validated @RequestBody CarGroupSaveRequest request
    ) {
        carGroupService.update(request);
        return AjaxResult.success();
    }

    @PostMapping("/list")
    public AjaxResult list(
            @Validated @RequestBody CarGroupListRequest request
    ) {
        return AjaxResult.success(carGroupService.list(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:carGroup:listPage')")
    @PostMapping("/listPage")
    public AjaxResult listPage(
            @Validated @RequestBody CarGroupListRequest request
    ) {
        return AjaxResult.success(carGroupService.listPage(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:carGroup:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(
            @Validated @RequestBody IdRequest request
    ) {
        return AjaxResult.success(carGroupService.detail(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:carGroup:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(
            @Validated @RequestBody IdRequest request
    ) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        carGroupService.delete(loginUser, request.getId());
        return AjaxResult.success();
    }
}
