package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.camera.request.CameraAddRequest;
import com.hean.nucleus.domain.camera.request.CameraListRequest;
import com.hean.nucleus.domain.camera.request.CameraUpdateRequest;
import com.hean.nucleus.domain.camera.service.CameraService;
import com.hean.nucleus.domain.ys7.request.Ys7CameraPreviewUrlRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 摄像头管理控制器
 */
@RestController
@RequestMapping("/camera")
public class CameraController {

    @Resource
    private CameraService cameraService;

    /**
     * 添加摄像头
     */
    @PreAuthorize("@ss.hasPermi('nucleus:camera:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody CameraAddRequest request) {
        cameraService.add(request);
        return AjaxResult.success();
    }

    /**
     * 更新摄像头
     */
    @PreAuthorize("@ss.hasPermi('nucleus:camera:update')")
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody CameraUpdateRequest request) {
        cameraService.update(request);
        return AjaxResult.success();
    }

    /**
     * 删除摄像头
     */
    @PreAuthorize("@ss.hasPermi('nucleus:camera:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(@Validated @RequestBody IdRequest request) {
        cameraService.delete(request);
        return AjaxResult.success();
    }

    /**
     * 摄像头分页列表查询
     */
    @PreAuthorize("@ss.hasPermi('nucleus:camera:list')")
    @PostMapping("/list")
    public AjaxResult list(@Validated @RequestBody CameraListRequest request) {
        return AjaxResult.success(cameraService.list(request));
    }

    /**
     * 摄像头详情查询
     */
    @PreAuthorize("@ss.hasPermi('nucleus:camera:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(cameraService.detail(request));
    }

    /**
     * 获取摄像头播放地址
     */
    @PreAuthorize("@ss.hasPermi('nucleus:camera:play')")
    @PostMapping("/getPlayUrl")
    public AjaxResult getPlayUrl(@Validated @RequestBody Ys7CameraPreviewUrlRequest request) {
        return AjaxResult.success(cameraService.getPlayUrl(request));
    }

}
