package com.hean.web.controller.nucleus;

import com.hean.common.constant.Constants;
import com.hean.common.core.domain.AjaxResult;
import com.hean.common.core.domain.model.OpenBody;
import com.hean.framework.web.service.SysLoginService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/open")
public class OpenController {

    @Resource
    private SysLoginService loginService;

    @PostMapping("/getAccessToken")
    public AjaxResult getAccessToken(@RequestBody OpenBody openBody) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.getAccessToken(openBody.getAppId(), openBody.getAppSecret());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

}
