package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.detector.request.*;
import com.hean.nucleus.domain.detector.service.DetectorService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/detector")
public class DetectorController {

    @Resource
    private DetectorService detectorService;

    /**
     * 查询 Radpac 探测器资源库列表
     */
    @PreAuthorize("@ss.hasPermi('nucleus:detector:listRadpacDetectors')")
    @PostMapping("/listRadpacDetectors")
    public AjaxResult listRadpacDetectors(@Validated @RequestBody RadpacDetectorListRequest request) {
        return AjaxResult.success(detectorService.listRadpacDetectors(request));
    }

    /**
     * 添加探测器
     */
    @PreAuthorize("@ss.hasPermi('nucleus:detector:add')")
    @PostMapping("add")
    public AjaxResult add(@Validated @RequestBody DetectorAddRequest request) {
        detectorService.add(request);
        return AjaxResult.success();
    }

    /**
     * 修改探测器
     */
    @PreAuthorize("@ss.hasPermi('nucleus:detector:update')")
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody DetectorUpdateRequest request) {
        detectorService.update(request);
        return AjaxResult.success();
    }

    /**
     * 删除探测器
     */
    @PreAuthorize("@ss.hasPermi('nucleus:detector:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(@Validated @RequestBody IdRequest request) {
        detectorService.delete(request);
        return AjaxResult.success();
    }


    @PreAuthorize("@ss.hasPermi('nucleus:detector:listPage')")
    @PostMapping("/listPage")
    public AjaxResult listPage(
            @Validated @RequestBody DetectorListRequest request
    ) {
        return AjaxResult.success(detectorService.listPage(request));
    }

    /**
     * 查询探测器详情
     */
    @PreAuthorize("@ss.hasPermi('nucleus:detector:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(detectorService.detail(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:detector:bind')")
    @PostMapping("/bind")
    public AjaxResult bind(
            @Validated @RequestBody DetectorBindRequest request
    ) {
        detectorService.bindCar(request);
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('nucleus:detector:unbind')")
    @PostMapping("/unbind")
    public AjaxResult unbind(
            @Validated @RequestBody IdRequest request
    ) {
        detectorService.unbindCar(request);
        return AjaxResult.success();
    }

}
