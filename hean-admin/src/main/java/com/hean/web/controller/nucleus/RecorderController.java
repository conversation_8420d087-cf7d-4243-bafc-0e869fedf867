package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.request.*;
import com.hean.nucleus.domain.recorder.service.RecorderService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 海康NVR设备控制器
 */
@RestController
@RequestMapping("/recorder")
public class RecorderController {

    @Resource
    private RecorderService recorderService;

    /**
     * 查询 Radpac NVR资源库列表
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:listRadpacRecorders')")
    @PostMapping("/listRadpacRecorders")
    public AjaxResult listRadpacRecorders(@Validated @RequestBody RadpacRecorderListRequest request) {
        return AjaxResult.success(recorderService.listRadpacRecorders(request));
    }

    /**
     * 添加海康NVR设备
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:add')")
    @PostMapping("add")
    public AjaxResult add(@Validated @RequestBody RecorderAddRequest request) {
        recorderService.add(request);
        return AjaxResult.success();
    }

    /**
     * 修改海康NVR设备
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:update')")
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody RecorderUpdateRequest request) {
        recorderService.update(request);
        return AjaxResult.success();
    }

    /**
     * 删除海康NVR设备
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(@Validated @RequestBody IdRequest request) {
        recorderService.delete(request);
        return AjaxResult.success();
    }

    /**
     * 分页查询海康NVR设备
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:list')")
    @PostMapping("/listPage")
    public AjaxResult listPage(@Validated @RequestBody RecorderListRequest request) {
        return AjaxResult.success(recorderService.listPage(request));
    }

    /**
     * 查询海康NVR设备详情
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(recorderService.detail(request));
    }

    /**
     * 修改布防状态
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:changeDefense')")
    @PostMapping("/changeDefense")
    public AjaxResult changeDefense(@Validated @RequestBody DefenseChangeRequest request) {
        recorderService.changeDefense(request);
        return AjaxResult.success();
    }

    /**
     * 绑定车辆
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:bind')")
    @PostMapping("/bind")
    public AjaxResult bind(@Validated @RequestBody RecorderBindRequest request) {
        recorderService.bindCar(request);
        return AjaxResult.success();
    }

    /**
     * 解绑车辆
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recorder:unbind')")
    @PostMapping("/unbind")
    public AjaxResult unbind(@Validated @RequestBody IdRequest request) {
        recorderService.unbindCar(request);
        return AjaxResult.success();
    }

}
