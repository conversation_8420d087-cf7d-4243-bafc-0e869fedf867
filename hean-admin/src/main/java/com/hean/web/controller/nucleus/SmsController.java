package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.support.request.SmsAddRequest;
import com.hean.nucleus.domain.support.request.SmsListRequest;
import com.hean.nucleus.domain.support.service.SmsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sms")
public class SmsController {

    @Resource
    private SmsService smsService;

    @PreAuthorize("@ss.hasPermi('nucleus:sms:send')")
    @PostMapping("/send")
    public AjaxResult send(
            @Validated @RequestBody SmsAddRequest request
    ) {
        smsService.addSmsRecord(request);
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('nucleus:sms:list')")
    @PostMapping("/list")
    public AjaxResult list(
            @Validated @RequestBody SmsListRequest request
    ) {
        return AjaxResult.success(smsService.listSmsPage(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:sms:detail')")
    @GetMapping("/detail")
    public AjaxResult detail(
            @RequestParam("id") Long id
    ) {
        return AjaxResult.success(smsService.getSmsRecordDetail(id));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:sms:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(
            @Validated @RequestBody IdRequest request
    ) {
        smsService.deleteSmsRecord(request.getId());
        return AjaxResult.success();
    }
}
