package com.hean.web.controller.nucleus;

import com.hean.common.core.controller.BaseController;
import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.request.AlarmProcessAddRequest;
import com.hean.nucleus.domain.recorder.request.RecorderAlarmListRequest;
import com.hean.nucleus.domain.recorder.service.RecorderAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * NVR设备报警管理
 */
@Api(tags = "NVR设备报警管理")
@RestController
@RequestMapping("/recorderAlarm")
public class RecorderAlarmController extends BaseController {

    @Resource
    private RecorderAlarmService recorderAlarmService;

    /**
     * 分页查询设备报警列表
     */
    @ApiOperation("分页查询设备报警列表")
    @PreAuthorize("@ss.hasPermi('nucleus:recorderAlarm:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody @Validated RecorderAlarmListRequest request) {
        return AjaxResult.success(recorderAlarmService.listPage(request));
    }

    /**
     * 获取设备报警详情
     */
    @ApiOperation("获取设备报警详情")
    @PreAuthorize("@ss.hasPermi('nucleus:recorderAlarm:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(@RequestBody @Validated IdRequest request) {
        return AjaxResult.success(recorderAlarmService.detail(request));
    }

    /**
     * 告警处置
     */
    @ApiOperation("告警处置")
    @PreAuthorize("@ss.hasPermi('nucleus:recorderAlarm:process')")
    @PostMapping("/process")
    public AjaxResult processAlarm(@RequestBody @Validated AlarmProcessAddRequest request) {
        recorderAlarmService.processAlarm(request);
        return AjaxResult.success();
    }

    /**
     * 删除告警处置
     */
    @ApiOperation("删除告警处置")
    @PreAuthorize("@ss.hasPermi('nucleus:recorderAlarm:deleteProcess')")
    @PostMapping("/deleteProcess")
    public AjaxResult deleteProcess(@RequestBody @Validated IdRequest request) {
        recorderAlarmService.deleteProcess(request);
        return AjaxResult.success();
    }

}
