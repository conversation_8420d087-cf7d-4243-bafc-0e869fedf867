package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.detector.request.DetectorDataListRequest;
import com.hean.nucleus.domain.detector.request.DetectorDataTrailRequest;
import com.hean.nucleus.domain.detector.service.DetectorDataService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/detectorData")
public class DetectorDataController {

    @Resource
    private DetectorDataService detectorDataService;

    @PreAuthorize("@ss.hasPermi('nucleus:detectorData:lastData')")
    @PostMapping("/lastData")
    public AjaxResult lastData(
            @Validated @RequestBody IdRequest request
    ) {
        return AjaxResult.success(detectorDataService.lastData(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:detectorData:trail')")
    @PostMapping("/trailData")
    public AjaxResult trailData(
            @Validated @RequestBody DetectorDataTrailRequest request
    ) {
        return AjaxResult.success(detectorDataService.trailData(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:detectorData:listDataPage')")
    @PostMapping("/listDataPage")
    public AjaxResult listDataPage(
            @Validated @RequestBody DetectorDataListRequest request
    ) {
        return AjaxResult.success(detectorDataService.listDataPage(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:detectorData:listData')")
    @PostMapping("/listData")
    public AjaxResult listData(
            @Validated @RequestBody DetectorDataListRequest request
    ) {
        return AjaxResult.success(detectorDataService.listData(request));
    }

}
