package com.hean.web.controller.nucleus;

import cn.hutool.core.util.XmlUtil;
import com.alibaba.fastjson2.JSON;
import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.domain.ys7.bean.Ys7WebhookMessage;
import com.hean.nucleus.domain.ys7.request.Ys7ListPageRequest;
import com.hean.nucleus.domain.ys7.service.Ys7Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/ys7")
@Slf4j
public class Ys7Controller {

    @Resource
    private Ys7Service ys7Service;

    @PreAuthorize("@ss.hasPermi('nucleus:ys7:listCameraResources')")
    @PostMapping("/listCameraResources")
    public AjaxResult listCameraResources(
            @Validated @RequestBody Ys7ListPageRequest request
    ) {
        return AjaxResult.success(ys7Service.listCameraResources(request));
    }

    @RequestMapping(value = "/webhook")
    public ResponseEntity<String> webhook(@RequestHeader HttpHeaders header, @RequestBody String body) {
        Ys7WebhookMessage receiveMessage = null;
        log.info("消息获取时间:{}, 请求头:{},请求体:{}", System.currentTimeMillis(), JSON.toJSONString(header), body);
        System.out.println("收到的消息:"+body);
        try {
            receiveMessage = JSON.parseObject(body, Ys7WebhookMessage.class);
            //todo:对收到的消息进行处理,最好发送到其他中间件,或者写到数据库中,不要影响回调地址的处理

            Map<String, Object> map = XmlUtil.xmlToMap(receiveMessage.getBody().getPayload());
            System.out.println("map:" + map);

        } catch (Exception e) {
            log.error("ys7 webhook body parse failed", e);
        }

        //必须进行返回
        Map<String, String> result = new HashMap<>(1);
        assert receiveMessage != null;
        String messageId = receiveMessage.getHeader().getMessageId();
        result.put("messageId", messageId);
        final ResponseEntity<String> resp = ResponseEntity.ok(JSON.toJSONString(result));
        log.info("返回的信息:{}",JSON.toJSONString(result));
        return resp;
    }
}
