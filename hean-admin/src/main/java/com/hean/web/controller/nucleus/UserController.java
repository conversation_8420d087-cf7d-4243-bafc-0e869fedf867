package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.common.core.domain.model.LoginUser;
import com.hean.common.utils.SecurityUtils;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.user.request.PasswordResetRequest;
import com.hean.nucleus.domain.user.request.UserAddRequest;
import com.hean.nucleus.domain.user.request.UserListRequest;
import com.hean.nucleus.domain.user.service.UserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;

    @PreAuthorize("@ss.hasPermi('nucleus:user:resetPassword')")
    @PostMapping("/resetPassword")
    public AjaxResult resetPassword(
            @Validated @RequestBody PasswordResetRequest request
    ) {
        userService.resetPassword(request);
        return AjaxResult.success();
    }

    //添加用户
    @PreAuthorize("@ss.hasPermi('nucleus:user:add')")
    @PostMapping("/add")
    public AjaxResult add(
            @Validated @RequestBody UserAddRequest request
    ) {
        userService.addUser(request);
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('nucleus:user:list')")
    @PostMapping("/list")
    public AjaxResult listDetail(
            @Validated @RequestBody UserListRequest request
    ) {
        return AjaxResult.success(userService.listUser(request));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:user:detail')")
    @GetMapping("/detail")
    public AjaxResult detail(
            @RequestParam("userId") Long userId
    ) {
        return AjaxResult.success(userService.detailUser(userId));
    }

    @PreAuthorize("@ss.hasPermi('nucleus:user:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(
            @Validated @RequestBody IdRequest request
    ) {
        //request中的id，传递userCompanyId
        LoginUser loginUser = SecurityUtils.getLoginUser();
        userService.delete(loginUser, request.getId());
        return AjaxResult.success();
    }
}
