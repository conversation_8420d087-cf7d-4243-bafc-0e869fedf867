package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * NVR设备日志管理
 */
@RestController
@RequestMapping("/recorderLog")
public class RecorderLogController {


//    /**
//     * 分页查询海康NVR设备日志
//     */
//    @PreAuthorize("@ss.hasPermi('nucleus:recorderLog:list')")
//    @PostMapping("/devlog/listPage")
//    public AjaxResult listDevLogPage(@Validated @RequestBody RadpacDevLogListRequest request) {
//        return AjaxResult.success(radpacDeviceService.listDevLogPage(request));
//    }
}
