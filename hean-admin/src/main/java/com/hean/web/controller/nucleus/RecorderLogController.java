package com.hean.web.controller.nucleus;

import com.hean.common.core.controller.BaseController;
import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.request.RecorderLogListRequest;
import com.hean.nucleus.domain.recorder.service.RecorderLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * NVR设备日志管理
 */
@Api(tags = "NVR设备日志管理")
@RestController
@RequestMapping("/recorderLog")
public class RecorderLogController extends BaseController {

    @Resource
    private RecorderLogService recorderLogService;

    /**
     * 分页查询设备日志列表
     */
    @ApiOperation("分页查询设备日志列表")
    @PreAuthorize("@ss.hasPermi('nucleus:recorderLog:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody @Validated RecorderLogListRequest request) {
        return AjaxResult.success(recorderLogService.listPage(request));
    }

    /**
     * 获取设备日志详情
     */
    @ApiOperation("获取设备日志详情")
    @PreAuthorize("@ss.hasPermi('nucleus:recorderLog:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(@RequestBody @Validated IdRequest request) {
        return AjaxResult.success(recorderLogService.detail(request));
    }

}
