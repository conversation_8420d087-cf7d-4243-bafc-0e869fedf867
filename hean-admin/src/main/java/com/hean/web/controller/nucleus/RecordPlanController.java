package com.hean.web.controller.nucleus;

import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/recordPlan")
public class RecordPlanController {

//    /**
//     * 添加海康NVR录像计划
//     */
//    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:add')")
//    @PostMapping("/recordplan/add")
//    public AjaxResult addRecordPlan(@Validated @RequestBody RadpacRecordPlanAddRequest request) {
//        radpacDeviceService.addRecordPlan(request);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 修改海康NVR录像计划
//     */
//    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:update')")
//    @PostMapping("/recordplan/update")
//    public AjaxResult updateRecordPlan(@Validated @RequestBody RadpacRecordPlanUpdateRequest request) {
//        radpacDeviceService.updateRecordPlan(request);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 删除海康NVR录像计划
//     */
//    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:delete')")
//    @GetMapping("/recordplan/delete")
//    public AjaxResult deleteRecordPlan(@RequestParam("id") Long id) {
//        radpacDeviceService.deleteRecordPlan(id);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 分页查询海康NVR录像计划
//     */
//    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:list')")
//    @PostMapping("/recordplan/listPage")
//    public AjaxResult listRecordPlanPage(@Validated @RequestBody RadpacRecordPlanListRequest request) {
//        return AjaxResult.success(radpacDeviceService.listRecordPlanPage(request));
//    }
//
//    /**
//     * 查询海康NVR录像计划详情
//     */
//    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:detail')")
//    @GetMapping("/recordplan/detail")
//    public AjaxResult detailRecordPlan(@RequestParam("id") Long id) {
//        return AjaxResult.success(radpacDeviceService.getRecordPlanDetail(id));
//    }

}
