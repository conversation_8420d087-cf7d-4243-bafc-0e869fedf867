package com.hean.web.controller.nucleus;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.request.RecordPlanAddRequest;
import com.hean.nucleus.domain.recorder.request.RecordPlanListRequest;
import com.hean.nucleus.domain.recorder.request.RecordPlanUpdateRequest;
import com.hean.nucleus.domain.recorder.service.RecordPlanService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/recordPlan")
public class RecordPlanController {

    @Resource
    private RecordPlanService recordPlanService;

    /**
     * 添加海康NVR录像计划
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RecordPlanAddRequest request) {
        recordPlanService.add(request);
        return AjaxResult.success();
    }

    /**
     * 修改海康NVR录像计划
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:update')")
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody RecordPlanUpdateRequest request) {
        recordPlanService.update(request);
        return AjaxResult.success();
    }

    /**
     * 删除海康NVR录像计划
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:delete')")
    @PostMapping("/delete")
    public AjaxResult delete(@Validated @RequestBody IdRequest request) {
        recordPlanService.delete(request);
        return AjaxResult.success();
    }

    /**
     * 分页查询海康NVR录像计划
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:list')")
    @PostMapping("/listPage")
    public AjaxResult listPage(@Validated @RequestBody RecordPlanListRequest request) {
        return AjaxResult.success(recordPlanService.listPage(request));
    }

    /**
     * 查询海康NVR录像计划详情
     */
    @PreAuthorize("@ss.hasPermi('nucleus:recordplan:detail')")
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(recordPlanService.detail(request));
    }

}
