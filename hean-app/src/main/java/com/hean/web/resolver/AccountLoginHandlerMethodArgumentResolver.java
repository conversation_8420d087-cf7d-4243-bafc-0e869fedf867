package com.hean.web.resolver;

import com.hean.nucleus.domain.user.model.TUser;
import com.hean.web.annotation.Account;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

@Component
public class AccountLoginHandlerMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType().isAssignableFrom(TUser.class) && parameter.hasParameterAnnotation(Account.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        Object object = webRequest.getAttribute("LOGIN_ACCOUNT", RequestAttributes.SCOPE_REQUEST);
        if(object == null) {
            return null;
        }

        if(object instanceof TUser) {
            return object;
        }

        return null;
    }
}
