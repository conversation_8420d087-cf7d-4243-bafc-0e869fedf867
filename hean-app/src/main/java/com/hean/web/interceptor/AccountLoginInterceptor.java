package com.hean.web.interceptor;

import com.hean.nucleus.domain.user.mapper.TUserMapper;
import com.hean.nucleus.domain.user.model.TUser;
import com.hean.web.annotation.AccountLogin;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class AccountLoginInterceptor implements HandlerInterceptor {

    @Value("${token.header}")
    private String header;

    @Value("${token.secret}")
    private String secret;

    @Resource
    private TUserMapper userMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if(handler instanceof HandlerMethod) {
            HandlerMethod method = (HandlerMethod)handler;
            if(method.hasMethodAnnotation(AccountLogin.class)) {
                String jwtToken = request.getHeader(header);
                if(jwtToken == null) {
                    throw new AccessDeniedException("invalid token");
                }

                Claims claims;
                try {
                    claims = Jwts.parser()
                            .setSigningKey(secret)
                            .parseClaimsJws(jwtToken)
                            .getBody();
                } catch (Exception e) {
                    throw new AccessDeniedException("token parse fail");
                }

                Long userId = claims.get("userId", Long.class);
                if(userId == null) {
                    throw new AccessDeniedException("invalid user id");
                }

                TUser user = userMapper.selectById(userId);
                if(user == null || user.getDeleteFlag() == 1) {
                    throw new AccessDeniedException("invalid user");
                }

                request.setAttribute("LOGIN_ACCOUNT", user);
            }
        }

        return HandlerInterceptor.super.preHandle(request, response, handler);
    }
}
