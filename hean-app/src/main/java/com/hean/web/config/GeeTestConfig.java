package com.hean.web.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置

 */
@Component
@ConfigurationProperties(prefix = "gee-test")
public class GeeTestConfig
{
    private static String captchaId;

    private static String captchaKey;

    private static String domain;

    public static String getCaptchaId() {
        return captchaId;
    }

    public void setCaptchaId(String captchaId) {
        GeeTestConfig.captchaId = captchaId;
    }

    public static String getCaptchaKey() {
        return captchaKey;
    }

    public void setCaptchaKey(String captchaKey) {
        GeeTestConfig.captchaKey = captchaKey;
    }

    public static String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        GeeTestConfig.domain = domain;
    }
}
