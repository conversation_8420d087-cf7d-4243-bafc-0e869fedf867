package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.camera.request.CameraAddRequest;
import com.hean.nucleus.domain.camera.request.CameraListRequest;
import com.hean.nucleus.domain.camera.request.CameraUpdateRequest;
import com.hean.nucleus.domain.camera.service.CameraService;
import com.hean.nucleus.domain.ys7.request.Ys7CameraPreviewUrlRequest;
import com.hean.web.annotation.AccountLogin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/camera")
public class CameraController {

    @Resource
    private CameraService cameraService;

    /**
     * 添加摄像头
     */
    @AccountLogin
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody CameraAddRequest request) {
        cameraService.add(request);
        return AjaxResult.success();
    }

    /**
     * 更新摄像头
     */
    @AccountLogin
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody CameraUpdateRequest request) {
        cameraService.update(request);
        return AjaxResult.success();
    }

    /**
     * 删除摄像头
     */
    @AccountLogin
    @PostMapping("/delete")
    public AjaxResult delete(@Validated @RequestBody IdRequest request) {
        cameraService.delete(request);
        return AjaxResult.success();
    }

    /**
     * 摄像头分页列表
     */
    @PostMapping("/list")
    public AjaxResult list(@Validated @RequestBody CameraListRequest request) {
        return AjaxResult.success(cameraService.list(request));
    }

    /**
     * 摄像头详情
     */
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(cameraService.detail(request));
    }

    /**
     * 获取播放地址
     */
    @PostMapping("/getPlayUrl")
    public AjaxResult getPlayUrl(@Validated @RequestBody Ys7CameraPreviewUrlRequest request) {
        return AjaxResult.success(cameraService.getPlayUrl(request));
    }

}
