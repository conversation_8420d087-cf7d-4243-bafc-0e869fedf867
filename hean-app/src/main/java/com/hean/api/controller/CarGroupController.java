package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.domain.car.request.CarGroupListRequest;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.service.CarGroupService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/carGroup")
public class CarGroupController {

    @Resource
    private CarGroupService carGroupService;

    //@AccountLogin
    @PostMapping("/list")
    public AjaxResult list(
            @Validated @RequestBody CarGroupListRequest request
    ) {
        return AjaxResult.success(carGroupService.listPage(request));
    }

    //@AccountLogin
    @PostMapping("/detail")
    public AjaxResult detail(
            @Validated @RequestBody IdRequest request
    ) {
        return AjaxResult.success(carGroupService.detail(request));
    }

}
