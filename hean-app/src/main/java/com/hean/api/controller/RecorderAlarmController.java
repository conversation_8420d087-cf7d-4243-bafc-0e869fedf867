package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.request.AlarmProcessAddRequest;
import com.hean.nucleus.domain.recorder.request.RecorderAlarmListRequest;
import com.hean.nucleus.domain.recorder.service.RecorderAlarmService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/recorderAlarm")
public class RecorderAlarmController {

    @Resource
    private RecorderAlarmService recorderAlarmService;

    /**
     * 分页查询设备报警列表
     */
    @PostMapping("/list")
    public AjaxResult list(@Validated @RequestBody RecorderAlarmListRequest request) {
        return AjaxResult.success(recorderAlarmService.listPage(request));
    }

    /**
     * 获取设备报警详情
     */
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(recorderAlarmService.detail(request));
    }

    /**
     * 告警处置
     */
    @PostMapping("/process")
    public AjaxResult processAlarm(@Validated @RequestBody AlarmProcessAddRequest request) {
        recorderAlarmService.processAlarm(request);
        return AjaxResult.success();
    }

    /**
     * 删除告警处置
     */
    @PostMapping("/deleteProcess")
    public AjaxResult deleteProcess(@Validated @RequestBody IdRequest request) {
        recorderAlarmService.deleteProcess(request);
        return AjaxResult.success();
    }

}
