package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.request.*;
import com.hean.nucleus.domain.recorder.service.RecorderService;
import com.hean.web.annotation.AccountLogin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 海康NVR设备控制器
 */
@RestController
@RequestMapping("/recorder")
public class RecorderController {

    @Resource
    private RecorderService recorderService;

    /**
     * 查询 Radpac NVR资源库列表
     */
    @PostMapping("/listRadpacRecorders")
    public AjaxResult listRadpacRecorders(@Validated @RequestBody RadpacRecorderListRequest request) {
        return AjaxResult.success(recorderService.listRadpacRecorders(request));
    }

    /**
     * 添加海康NVR设备
     */
    @AccountLogin
    @PostMapping("add")
    public AjaxResult add(@Validated @RequestBody RecorderAddRequest request) {
        recorderService.add(request);
        return AjaxResult.success();
    }

    /**
     * 修改海康NVR设备
     */
    @AccountLogin
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody RecorderUpdateRequest request) {
        recorderService.update(request);
        return AjaxResult.success();
    }

    /**
     * 删除海康NVR设备
     */
    @AccountLogin
    @PostMapping("/delete")
    public AjaxResult delete(@Validated @RequestBody IdRequest request) {
        recorderService.delete(request);
        return AjaxResult.success();
    }

    /**
     * 分页查询海康NVR设备
     */
    @AccountLogin
    @PostMapping("/listPage")
    public AjaxResult listPage(@Validated @RequestBody RecorderListRequest request) {
        return AjaxResult.success(recorderService.listPage(request));
    }

    /**
     * 查询海康NVR设备详情
     */
    @AccountLogin
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(recorderService.detail(request));
    }

    /**
     * 修改布防状态
     */
    @AccountLogin
    @PostMapping("/changeDefense")
    public AjaxResult changeDefense(@Validated @RequestBody DefenseChangeRequest request) {
        recorderService.changeDefense(request);
        return AjaxResult.success();
    }

    /**
     * 绑定车辆
     */
    @AccountLogin
    @PostMapping("/bind")
    public AjaxResult bind(@Validated @RequestBody RecorderBindRequest request) {
        recorderService.bindCar(request);
        return AjaxResult.success();
    }

    /**
     * 解绑车辆
     */
    @AccountLogin
    @PostMapping("/unbind")
    public AjaxResult unbind(@Validated @RequestBody IdRequest request) {
        recorderService.unbindCar(request);
        return AjaxResult.success();
    }
}
