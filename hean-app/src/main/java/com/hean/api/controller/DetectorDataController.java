package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.detector.request.DetectorDataTrailRequest;
import com.hean.nucleus.domain.detector.service.DetectorDataService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/detectorData")
public class DetectorDataController {

    @Resource
    private DetectorDataService detectorDataService;

    //@AccountLogin
    @GetMapping("/lastData")
    public AjaxResult lastData(
            @Validated @RequestBody IdRequest request
    ) {
        return AjaxResult.success(detectorDataService.lastData(request));
    }

    //@AccountLogin
    @PostMapping("/trailData")
    public AjaxResult trailData(
            @Validated @RequestBody DetectorDataTrailRequest request
    ) {
        return AjaxResult.success(detectorDataService.trailData(request));
    }

}
