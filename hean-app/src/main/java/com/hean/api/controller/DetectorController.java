package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.detector.request.*;
import com.hean.nucleus.domain.detector.service.DetectorService;
import com.hean.web.annotation.AccountLogin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/detector")
public class DetectorController {

    @Resource
    private DetectorService detectorService;

    /**
     * 查询 Radpac 探测器资源库列表
     */
    @PostMapping("/listRadpacDetectors")
    public AjaxResult listRadpacDetectors(@Validated @RequestBody RadpacDetectorListRequest request) {
        return AjaxResult.success(detectorService.listRadpacDetectors(request));
    }

    /**
     * 添加探测器
     */
    @AccountLogin
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody DetectorAddRequest request) {
        detectorService.add(request);
        return AjaxResult.success();
    }

    /**
     * 更新探测器
     */
    @AccountLogin
    @PostMapping("/update")
    public AjaxResult update(@Validated @RequestBody DetectorUpdateRequest request) {
        detectorService.update(request);
        return AjaxResult.success();
    }

    /**
     * 绑定车辆
     */
    @AccountLogin
    @PostMapping("/bindCar")
    public AjaxResult bindCar(@Validated @RequestBody DetectorBindRequest request) {
        detectorService.bindCar(request);
        return AjaxResult.success();
    }

    /**
     * 解绑车辆
     */
    @AccountLogin
    @PostMapping("/unbindCar")
    public AjaxResult unbindCar(@Validated @RequestBody IdRequest request) {
        detectorService.unbindCar(request);
        return AjaxResult.success();
    }

    /**
     * 删除探测器
     */
    @AccountLogin
    @PostMapping("/delete")
    public AjaxResult delete(@Validated @RequestBody IdRequest request) {
        detectorService.delete(request);
        return AjaxResult.success();
    }

    /**
     * 分页查询探测器列表
     */
    @PostMapping("/list")
    public AjaxResult list(@Validated @RequestBody DetectorListRequest request) {
        return AjaxResult.success(detectorService.listPage(request));
    }

    /**
     * 查询探测器详情
     */
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(detectorService.detail(request));
    }

}
