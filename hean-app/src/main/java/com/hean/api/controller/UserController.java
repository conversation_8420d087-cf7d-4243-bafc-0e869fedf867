package com.hean.api.controller;

import com.hean.api.vo.LoginVo;
import com.hean.common.core.domain.AjaxResult;
import com.hean.common.utils.uuid.IdUtils;
import com.hean.nucleus.domain.user.model.TUser;
import com.hean.nucleus.domain.user.service.UserService;
import com.hean.web.annotation.Account;
import com.hean.web.annotation.AccountLogin;
import com.hean.web.service.CaptchaService;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private CaptchaService captchaService;

    @Value("${token.secret}")
    private String secret;

    @PostMapping("/login")
    public AjaxResult login(
            @Validated @RequestBody LoginVo vo
    ) {
        captchaService.validateCaptcha(vo.getCaptchaCode(), vo.getCaptchaUuid());

        TUser userModel = userService.login(vo.getAccount(), vo.getPassword());

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userModel.getId());
        claims.put("uuid", IdUtils.fastUUID());

        Map<String, String> data = new HashMap<>();
        data.put("token", Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret).compact());

        return AjaxResult.success(data);
    }

    @AccountLogin
    @GetMapping("/getDetail")
    public AjaxResult getDetail(
            @Account TUser user
    ) {
        return AjaxResult.success(userService.detailUser(user.getId()));
    }
}
