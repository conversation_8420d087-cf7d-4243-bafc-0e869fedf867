package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.web.annotation.AccountLogin;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/recorderLog")
public class RecorderLogController {

//    /**
//     * 分页查询海康NVR设备日志
//     */
//    @AccountLogin
//    @PostMapping("/devlog/listPage")
//    public AjaxResult listDevLogPage(@Validated @RequestBody RadpacDevLogListRequest request) {
//        return AjaxResult.success(radpacDeviceService.listDevLogPage(request));
//    }
//

}
