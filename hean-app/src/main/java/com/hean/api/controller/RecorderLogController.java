package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.recorder.request.RecorderLogListRequest;
import com.hean.nucleus.domain.recorder.service.RecorderLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/recorderLog")
public class RecorderLogController {

    @Resource
    private RecorderLogService recorderLogService;

    /**
     * 分页查询设备日志列表
     */
    @PostMapping("/list")
    public AjaxResult list(@Validated @RequestBody RecorderLogListRequest request) {
        return AjaxResult.success(recorderLogService.listPage(request));
    }

    /**
     * 获取设备日志详情
     */
    @PostMapping("/detail")
    public AjaxResult detail(@Validated @RequestBody IdRequest request) {
        return AjaxResult.success(recorderLogService.detail(request));
    }

}
