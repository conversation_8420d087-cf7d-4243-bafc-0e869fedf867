package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.web.service.CaptchaService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 验证码操作处理
 */
@RestController
public class CaptchaController {

    @Resource
    private CaptchaService captchaService;

    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException {
        Map<String, String> result = captchaService.createCaptcha();
        return AjaxResult.success(result);
    }

}
