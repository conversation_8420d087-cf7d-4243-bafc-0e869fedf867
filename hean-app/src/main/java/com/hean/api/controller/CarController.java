package com.hean.api.controller;

import com.hean.common.core.domain.AjaxResult;
import com.hean.nucleus.domain.car.request.CarListRequest;
import com.hean.nucleus.common.request.IdRequest;
import com.hean.nucleus.domain.car.service.CarService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/car")
public class CarController {

    @Resource
    private CarService carService;

    //@AccountLogin
    @PostMapping("/list")
    public AjaxResult list(
            @Validated @RequestBody CarListRequest request
    ) {
        return AjaxResult.success(carService.listPage(request));
    }

    //@AccountLogin
    @PostMapping("/detail")
    public AjaxResult detail(
            @Validated @RequestBody IdRequest request
    ) {
        return AjaxResult.success(carService.detail(request));
    }


}
