# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 通讯规范

- 永远使用简体中文进行思考和对话

## 项目概述

核安（hean）是一个基于Java 11构建的多模块Spring Boot应用系统，专门用于核安全监测。项目采用模块化架构，将不同关注点分离到独立的模块中。项目版本为1.0.0。

## 项目结构

项目由8个Maven模块组成：

- **hean-admin**: 管理端入口，端口10098
- **hean-app**: 小程序端入口，端口10097
- **hean-nucleus**: 核心业务领域逻辑（相机、车辆、探测器、记录仪、报警）
- **hean-framework**: 框架组件（安全、认证、AOP）
- **hean-system**: 系统管理（用户、角色、权限、配置）
- **hean-quartz**: 定时任务管理
- **hean-database**: 数据库配置和数据源管理
- **hean-common**: 共享工具类和通用组件

## 核心技术栈

- **框架**: Spring Boot 2.5.14
- **数据库**: MyBatis Plus 3.5.1 + 达梦数据库（DaMeng）8.1.3.140版本
- **连接池**: Druid 1.2.15
- **安全框架**: Spring Security + JWT令牌
- **API文档**: Swagger 3.0.0（Springfox）
- **任务调度**: Quartz
- **构建工具**: Maven
- **Java版本**: 11
- **JSON解析**: FastJSON2 2.0.22
- **工具类**: Lombok 1.18.24
- **分页插件**: PageHelper 1.4.5
- **Office文档**: Apache POI 5.2.3
- **模板引擎**: Apache Velocity 2.3

## 构建和运行命令

### 开发环境
```bash
# 构建整个项目
mvn clean compile

# 运行管理界面（端口10098）
cd hean-admin
mvn spring-boot:run

# 运行移动应用（端口10097）
cd hean-app  
mvn spring-boot:run

# 打包部署
mvn clean package
```

### 测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UserServiceTest

# 在特定模块运行测试
cd hean-app
mvn test
```

## 架构模式

### 领域驱动设计（DDD）
nucleus模块按业务领域组织逻辑：
- `camera/`: 视频监控系统集成
- `car/`: 车辆跟踪和管理
- `detector/`: 辐射探测设备
- `recorder/`: 数据记录和日志
- `user/`: 业务上下文中的用户管理
- `ys7/`: 第三方视频服务集成

### 双应用架构
- **hean-admin**: 功能完整的Web管理面板
- **hean-app**: 轻量级移动/API接口
- 两个应用通过nucleus模块共享相同的业务逻辑

### 数据访问模式
- MyBatis XML映射文件位于 `src/main/resources/mapper/`
- 请求/响应DTO与领域模型分离
- Service层包含业务逻辑
- Mapper接口进行数据访问

## 配置环境

应用支持多环境配置：
- `dev`: 开发环境
- `online`: 生产环境

配置文件位于 `src/main/resources/`：
- `application.yml`: 主配置文件
- `application-dev.yml`: 开发环境覆盖配置
- `application-online.yml`: 生产环境覆盖配置

## 重要配置信息

### 端口配置
- **hean-admin**: 10098（Web管理界面）
- **hean-app**: 10097（移动/API应用）

### 数据库连接
- **数据库**: 达梦数据库（DaMeng）8.1.3.140版本
- **连接地址**: jdbc:dm://*************:17436/nucleus
- **用户名**: SYSDBA
- **连接池**: Druid 1.2.15（监控界面：/druid/）
- **监控账号**: 用户名`hean`，密码`123456`
- **支持主从数据源动态切换**（当前从库未启用）
- **MyBatis配置文件**: `mybatis-config.xml`

### 缓存配置
- **Redis**: 阿里云Redis实例
- **地址**: r-bp1m2dmndwoc19giwcpd.redis.rds.aliyuncs.com:6379
- **数据库索引**: admin模块使用168，app模块使用169

### 第三方服务集成
- **YS7萤石云**: 视频监控服务（appKey: 7b3a614f80a34b3b93e00c3cb09c4294）
- **Cipher加密**: SM2/SM3/SM4国密算法支持（服务地址: http://*************:19108）
- **OAuth认证**: 第三方单点登录（clientId: chezaiyidongjiance）

### 应用配置
- **文件上传路径**: D:/hean/uploadPath（开发环境）
- **验证码类型**: math（数学计算验证码）
- **Token有效期**: 30分钟
- **密码策略**: 最大错误5次，锁定10分钟

## 安全实现

- JWT令牌认证（30分钟有效期）
- Spring Security集成
- 基于角色的访问控制（RBAC）
- 自定义密码编码
- API端点保护
- XSS攻击防护

## 核心业务领域

1. **核安全监测**: 辐射探测和监控核心功能
2. **视频监控**: 摄像头管理和萤石云直播
3. **车辆跟踪**: 车辆和车组管理
4. **报警处理**: 实时警报处理和记录
5. **数据记录**: 历史数据存储和检索

## 数据库表结构

`radpac_`前缀开头的表结构是由另外一个团队在维护，其它的表结构都是由本项目来进行管理维护。

### 系统管理表（系统框架表）
基于经典的RBAC权限管理模式：

- **sys_user**: 用户信息表，存储系统用户基本信息
- **sys_role**: 角色信息表，定义系统角色
- **sys_menu**: 菜单权限表，树形结构存储菜单和按钮权限
- **sys_dept**: 部门表，树形结构管理组织架构
- **sys_post**: 岗位信息表
- **sys_user_role**: 用户角色关联表（多对多）
- **sys_role_menu**: 角色菜单关联表（多对多）
- **sys_role_dept**: 角色部门关联表（多对多）
- **sys_user_post**: 用户岗位关联表（多对多）

### 系统配置表
- **sys_config**: 参数配置表，存储系统参数
- **sys_dict_type**: 字典类型表
- **sys_dict_data**: 字典数据表

### 日志审计表
- **sys_oper_log**: 操作日志记录表
- **sys_logininfor**: 系统访问记录表
- **sys_notice**: 通知公告表

### 任务调度表（Quartz）
- **sys_job**: 定时任务调度表
- **sys_job_log**: 定时任务调度日志表

### 核心业务表（核安全监测）

#### 车辆管理
- **t_car**: 汽车表，存储车辆基本信息（车牌号、司机等）
- **t_car_group**: 汽车分组表，车辆分组管理

#### 摄像头监控
- **t_camera**: 摄像头表，集成萤石云和海康威视
  - 支持萤石云通道绑定（ys7ChannelNo, ys7ChannelName）
  - 支持海康威视（hkIndexCode）
  - 与记录仪和车辆关联

#### 辐射探测器
- **t_detector**: 探测器主表，探测设备基本信息
- **radpac_info**: Radpac探测器信息表，本项目中只读取其中的数据，不做任何修改
- **radpac_data**: 探测器数据记录表，本项目中只读取其中的数据，不做任何修改
  - 存储GPS位置（gpsx, gpsy）
  - 环境数据（温度、湿度、气压）
  - 辐射探测数据（sensorData）
  - 设备状态（电池电量等）

#### 记录仪管理
- **t_recorder**: 记录仪主表
- **radpac_record_plan**: 记录计划表，本项目中会读取和修改其中的数据
- **radpac_dev_info**: 设备信息表，本项目中会读取其中的数据，只会修改其中`DEFENSE_SET`字段的值
- **radpac_dev_log**: 设备日志表，本项目中只读取其中的数据，不做任何修改

#### 报警处理
- **t_alarm_process**: 报警处理表，处理各种报警事件
- **radpac_alarm**: 辐射报警记录表，本项目中只读取其中的数据，不做任何修改

#### 用户管理（业务层面）
- **t_user**: 业务用户表，与系统用户表分离
- **t_sms**: 短信记录表，用于报警通知

### 表命名规范
- **系统表**: `sys_` 前缀
- **业务表**: `t_` 前缀（简单业务表）
- **Radpac设备表**: `radpac_` 前缀（辐射探测相关）开头的表结构是由另外一个团队在维护
- **代码生成表**: `gen_` 前缀

### 字段设计规范
- 主键：通常使用 `id`，自增长
- 时间字段：`create_time`、`update_time`、`delete_time`
- 删除标记：`delete_flag`（0=未删除，1=已删除）
- 状态字段：通常使用 `status`
- 外键关联：使用具体的ID字段名，如 `car_id`、`user_id`

## 重要提醒

### Maven仓库配置
- 使用阿里云Maven镜像：https://maven.aliyun.com/repository/public
- 额外仓库：https://repo.e-iceblue.cn/repository/maven-public/（用于特殊依赖）

### 第三方JAR包
- hean-nucleus模块包含本地JAR包：artemis-http-client-1.1.12.RELEASE.jar（海康威视SDK）

### SQL脚本
- `sql/ry_20220822.sql`: 主要数据库结构脚本
- `sql/quartz.sql`: Quartz任务调度表结构

## 开发要点

- **Lombok减少样板代码**：使用1.18.24版本
- **Swagger API文档**：`/swagger-ui/`（开发环境启用）
- **Redis缓存提升性能**：阿里云Redis实例
- **自定义线程池异步任务处理**：配置在ThreadPoolConfig中
- **文件上传功能和验证**：支持10MB单文件，20MB总大小限制
- **Excel导入导出功能**：基于Apache POI 5.2.3
- **国际化支持（i18n）**：资源文件位于`i18n/messages.properties`
- **Druid数据库监控**：访问`/druid/`，用户名`hean`，密码`123456`
- **XSS攻击防护**：可配置过滤规则
- **防重复提交拦截器**：自定义注解@RepeatSubmit
- **数据权限控制**：自定义注解@DataScope
- **操作日志记录**：自定义注解@Log
- **限流功能**：自定义注解@RateLimiter