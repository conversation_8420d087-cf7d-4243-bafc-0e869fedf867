package com.hean.common.utils;

import java.util.regex.Pattern;

public class CurrencyUtils {
    //无效字符
    private static final Pattern INVALID_CHARACTER = Pattern.compile("[^,.\\d]");
    //有效数字格式
    private static final Pattern VALID_DIGIT = Pattern
            .compile("^((\\d{1,3}(,\\d{3})*(.((\\d{3},)*\\d{1,3}))?)|(\\d+(.\\d+)?))$");
    //最大值999亿多
    private static final double MAXIMUM_NUMBER = 99999999999.99;
    //货币符号
    private static final String CN_ZERO = "零";
    private static final String CN_ONE = "壹";
    private static final String CN_TWO = "贰";
    private static final String CN_THREE = "叁";
    private static final String CN_FOUR = "肆";
    private static final String CN_FIVE = "伍";
    private static final String CN_SIX = "陆";
    private static final String CN_SEVEN = "柒";
    private static final String CN_EIGHT = "捌";
    private static final String CN_NINE = "玖";
    private static final String CN_TEN = "拾";
    private static final String CN_HUNDRED = "佰";
    private static final String CN_THOUSAND = "仟";
    private static final String CN_TEN_THOUSAND = "万";
    private static final String CN_HUNDRED_MILLION = "亿";
    private static final String CN_DOLLAR = "元";
    private static final String CN_TEN_CENT = "角";
    private static final String CN_CENT = "分";
    private static final String CN_INTEGER = "整";
    //整数 0-9
    private static final String[] DIGITS = {
            CN_ZERO, CN_ONE, CN_TWO, CN_THREE, CN_FOUR, CN_FIVE, CN_SIX, CN_SEVEN, CN_EIGHT, CN_NINE};
    //基数 百 千
    private static final String[] RADICES = {"", CN_TEN, CN_HUNDRED, CN_THOUSAND};
    //大基数 万 亿
    private static final String[] BIGRADICES = {"", CN_TEN_THOUSAND, CN_HUNDRED_MILLION};
    //小数 角 分
    private static final String[] DECIMALS = {CN_TEN_CENT, CN_CENT};

    public static String convertCurrency(String currencyDigits) {
        if (currencyDigits == null || currencyDigits.length() == 0) {
            System.out.println("不能为空");
            return "";
        }
        //字符串中不能包含除了逗号点号和数字的其他字符
        if (INVALID_CHARACTER.matcher(currencyDigits).matches()) {
            System.out.println("包含无效字符");
            return "";
        }
        if (!VALID_DIGIT.matcher(currencyDigits).matches()) {
            System.out.println("数字格式错误");
            return "";
        }
        //移除所有的逗号
        currencyDigits = currencyDigits.replaceAll(",", "");
        //移除所有的前导零
        currencyDigits = currencyDigits.replaceAll("^0+", "");
        // Assert the number is not greater than the maximum number.
        if (Double.parseDouble(currencyDigits) > MAXIMUM_NUMBER) {
            return "";
        }
        //根据点号分割成两部分整数和小数
        String[] parts = currencyDigits.split("\\.");
        //整数
        String integral;
        //小数
        String decimal;
        if (parts.length > 1) {
            integral = parts[0];
            decimal = parts[1];
            //小数最多保留2位
            if (decimal.length() >= 2) {
                decimal = decimal.substring(0, 2);
            } else {
                decimal = decimal.substring(0, 1);
            }
        } else {
            integral = parts[0];
            decimal = "";
        }
        StringBuilder outputCharacters = new StringBuilder();
        //处理整数
        if (Integer.parseInt(integral) > 0) {
            int zeroCount = 0;
            for (int i = 0; i < integral.length(); i++) {
                int p = integral.length() - i - 1;
                int d = integral.charAt(i) - '0';
                int quotient = p / 4;
                int modulus = p % 4;
                if (d == 0) {
                    zeroCount++;
                } else {
                    if (zeroCount > 0) {
                        outputCharacters.append(DIGITS[0]);
                    }
                    zeroCount = 0;
                    outputCharacters.append(DIGITS[d]).append(RADICES[modulus]);
                }
                if (modulus == 0 && zeroCount < 4) {
                    outputCharacters.append(BIGRADICES[quotient]);
                }
            }
            outputCharacters.append(CN_DOLLAR);
        }
        //处理小数
        if (!decimal.isEmpty()) {
            for (int i = 0; i < decimal.length(); i++) {
                int d = decimal.charAt(i) - '0';
                int ds = decimal.charAt(decimal.length() - 1) - '0';
                if (d == 0) {
                    if (ds != 0) {
                        outputCharacters.append(DIGITS[d]);
                    }
                } else {
                    outputCharacters.append(DIGITS[d]).append(DECIMALS[i]);
                }
            }
        }
        if (outputCharacters.length() == 0) {
            outputCharacters.append(CN_ZERO).append(CN_DOLLAR);
        }
        if (decimal.isEmpty()) {
            outputCharacters.append(CN_INTEGER);
        }
        return outputCharacters.toString();
    }


}
