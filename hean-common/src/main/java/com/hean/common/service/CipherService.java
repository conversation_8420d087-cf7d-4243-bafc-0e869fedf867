package com.hean.common.service;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import cn.hutool.core.util.HexUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hean.common.core.redis.RedisCache;
import com.hean.common.exception.ServiceException;

import cn.hutool.crypto.SmUtil;


@Service
@Slf4j
public class CipherService {

    @Value("${cipher.prefix}")
    private String prefix;

    @Value("${cipher.clientId}")
    private String clientId;

    @Value("${cipher.clientSecret}")
    private String clientSecret;

    @Value("${cipher.serverId}")
    private String serverId;

    @Value("${cipher.applicationId}")
    private String applicationId;

    @Value("${cipher.appSecret}")
    private String appSecret;

    @Value("${cipher.sm2KeyId}")
    private String sm2KeyId;

    @Value("${cipher.sm3KeyId}")
    private String sm3KeyId;

    @Value("${cipher.sm4KeyId}")
    private String sm4KeyId;

    @Resource
    private RedisCache redisCache;

    // curl -X POST -H "Authorization: Basic OGhJckdCajA3TUFNTXJVZDpEUFRKQWJQSGJMZnd5ZE5IN3N1Tk9SWWczREtEVlk3eA==" -H "Content-Type: application/x-www-form-urlencoded;charset=utf-8" -d "grant_type=password&credentials=eyJhbGdvcml0aG0iOiIxNTg0My00LTUuMS4xIiwicmVzcG9uc2UiOiJDbG83ZVFKLzFCWWZlbFQ2bSs2a2MvM1RhZEkzdlYybUQ0b1RjVDB0bnpnPSIsInVzZXJfaWQiOiI2ODM3YzI5Mjc3M2I2ZDY5ZDNjMjgyM2IiLCJzZXJ2ZXJfaWQiOiJhdXRoX2NlbnRlciIsInRpbWVzdGFtcCI6MTc1MTI2ODA1NDU0Nn0=" --insecure https://***********:443/auth_center/cipher/token

    private JSONObject postForm(String path, String authorization, Map<String, String> params) {
        String url = prefix + path;

        log.info("商密接口调用 --> url:{} --> authorization:{} --> params:{}", url, authorization, JSON.toJSONString(params));

        HttpRequest request = HttpRequest.post(url)
                .header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
                .header("Authorization", authorization)
                .formStr(params);

        try(HttpResponse response = request.execute()) {
            if(!response.isOk()) {
                throw new ServiceException("请求失败");
            }
            String body = response.body();
            log.info("商密接口调用 --> url:{} --> authorization:{} --> params:{} --> response:{}", url, authorization, JSON.toJSONString(params), body);
            return JSON.parseObject(body);
        } catch (Exception e) {
            throw new ServiceException("请求失败", e);
        }
    }

    private JSONObject postJsonMap(String path, String authorization, HashMap<String, Object> params) {
        String url = prefix + path;

        log.info("商密接口调用 --> url:{} --> authorization:{} --> params:{}", url, authorization, JSON.toJSONString(params));

        HttpRequest request = HttpRequest.post(url)
                .header("Content-Type", "application/json;charset=utf-8")
                .header("Authorization", authorization)
                .body(JSON.toJSONString(params));

        try(HttpResponse response = request.execute()) {
            if(!response.isOk()) {
                throw new ServiceException("请求失败");
            }
            String body = response.body();
            log.info("商密接口调用 --> url:{} --> authorization:{} --> params:{} --> response:{}", url, authorization, JSON.toJSONString(params), body);
            return JSON.parseObject(body);
        } catch (Exception e) {
            throw new ServiceException("请求失败", e);
        }
    }

    public synchronized String getAccessToken() {
        String accessToken = redisCache.getCacheObject("cipher:accessToken");
        if(accessToken != null) {
            log.info("商密服务 --> 从缓存中获取accessToken: {}", accessToken);
            return "Bearer " + accessToken;
        }

        Long timestamp = System.currentTimeMillis();

        JSONObject credentials = new JSONObject();
        credentials.put("algorithm", "15843-4-5.1.1");
        credentials.put("response", Base64.getEncoder().encodeToString(HexUtil.decodeHex(SmUtil.sm3(timestamp + serverId + appSecret))));
        credentials.put("user_id", applicationId);
        credentials.put("server_id", serverId);
        credentials.put("timestamp", timestamp);

        log.info("商密服务 --> 获取accessToken");

        JSONObject result = postForm(
                "/auth_center/cipher/token",
                "Basic " + Base64.getEncoder().encodeToString((clientId + ":" + clientSecret).getBytes(StandardCharsets.UTF_8)),
                Map.of("grant_type", "password", "credentials", Base64.getEncoder().encodeToString(credentials.toJSONString().getBytes(StandardCharsets.UTF_8)))
        );

        if(result.containsKey("retcode")) {
            throw new ServiceException("获取商密accessToken失败: " + result);
        }

        accessToken = result.getString("access_token");
        if(accessToken == null) {
            throw new ServiceException("获取商密accessToken失败: " + result);
        }

        redisCache.setCacheObject("cipher:accessToken", accessToken, 800, TimeUnit.SECONDS);
        log.info("商密服务 --> 获取accessToken成功: {}", accessToken);
        return "Bearer " + accessToken;
    }


    public String sm2Encrypt(String value) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();
        // hex 编码
//        dataMap.put("secretKeyCode", sm2KeyId);
//        dataMap.put("algorithm", 2);
//        dataMap.put("requestEncode", 1);
//        dataMap.put("responseEncode", 1);
//        dataMap.put("mode", 0);
//        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
//        dataMap.put("plainText", HexUtil.encodeHexStr(value.getBytes(StandardCharsets.UTF_8)));
//        params.put("alg", 0);
//        params.put("data", dataMap);

        // base64 编码
        dataMap.put("secretKeyCode", sm2KeyId);
        dataMap.put("algorithm", 2);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("mode", 0);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("plainText", Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8)));
        params.put("alg", 0);
        params.put("data", dataMap);
        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataEncrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return contentJson.getString("data");
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public String sm2Decrypt(String encryptData) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        // hex 编码
//        dataMap.put("secretKeyCode", sm2KeyId);
//        dataMap.put("algorithm", 2);
//        dataMap.put("requestEncode", 1);
//        dataMap.put("responseEncode", 1);
//        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
//        dataMap.put("cipherText", encryptData);
//        params.put("alg", 0);
//        params.put("data", dataMap);

        // base64 编码
        dataMap.put("secretKeyCode", sm2KeyId);
        dataMap.put("algorithm", 2);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("cipherText", encryptData);
        params.put("alg", 0);
        params.put("data", dataMap);

        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataDecrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
//            return HexUtil.decodeHexStr(contentJson.getString("data"));
            return new String(Base64.getDecoder().decode(contentJson.getString("data")), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public boolean sm2Compare(String encryptValue, String value) {
        return sm2Decrypt(encryptValue).equals(value);
    }

    public String sm4Encrypt(String value) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        log.info("商密服务 --> SM4加密");

        // base64 编码
        dataMap.put("secretKeyCode", sm4KeyId);
        dataMap.put("algorithm", 1);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("mode", 0);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("plainText", Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8)));
        params.put("alg", 0);
        params.put("data", dataMap);
        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataEncrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return contentJson.getString("data");
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    /**
     * 解密数据并校验数据完整性
     *
     * @param encryptData 加密的数据
     * @return 解密后的数据
     */
    public String sm4Decrypt(String encryptData) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        log.info("商密接口调用 --> SM4解密");

        // base64 编码
        dataMap.put("secretKeyCode", sm4KeyId);
        dataMap.put("algorithm", 1);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("iv", HexUtil.encodeHexStr(new byte[16]));
        dataMap.put("cipherText", encryptData);
        params.put("alg", 0);
        params.put("data", dataMap);

        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataDecrypt", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return new String(Base64.getDecoder().decode(contentJson.getString("data")), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public boolean sm4Compare(String encryptValue, String value) {
        return sm4Decrypt(encryptValue).equals(value);
    }

    public String calcHash(String value) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> dataMap = new HashMap<>();

        log.info("商密服务 --> 计算HASH");

        // base64 编码
        dataMap.put("secretKeyCode", sm3KeyId);
        dataMap.put("algorithm", 5);
        dataMap.put("requestEncode", 2);
        dataMap.put("responseEncode", 2);
        dataMap.put("message", Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8)));
        params.put("alg", 0);
        params.put("data", dataMap);
        JSONObject jsonObject = postJsonMap("/cloud_cipher_svs/dataHash", getAccessToken(), params);
        try {
            JSONObject contentJson = jsonObject.getJSONObject("content");
            return contentJson.getString("data");
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + jsonObject.toJSONString(), e);
        }
    }

    public void checkHash(String value, String itemHash) {

        log.info("商密完整性校验 --> 待核验密文: {} --> 对比HASH: {}", value, itemHash);

        String calcHash = calcHash(value);

        if(!calcHash.equals(itemHash)) {
            throw new ServiceException("数据校验失败，加密数据可能被篡改");
        }

        log.info("商密完整性校验 --> 待核验密文: {} --> 对比HASH: {}，核验通过", value, itemHash);
    }

}
