package com.hean.common.core;

import com.hean.common.service.CipherService;
import org.springframework.security.crypto.password.PasswordEncoder;

public class CipherPasswordEncoder implements PasswordEncoder {

    private final CipherService cipherService;

    public CipherPasswordEncoder(CipherService cipherService) {
        this.cipherService = cipherService;
    }

    @Override
    public String encode(CharSequence rawPassword) {
        return cipherService.sm4Encrypt(rawPassword.toString());
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        return cipherService.sm4Decrypt(encodedPassword).equals(rawPassword.toString());
    }
}
