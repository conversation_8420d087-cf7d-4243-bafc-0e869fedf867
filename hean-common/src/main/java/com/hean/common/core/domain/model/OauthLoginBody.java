package com.hean.common.core.domain.model;

import javax.validation.constraints.NotBlank;

/**
 * 登录用户身份权限

 */
public class OauthLoginBody {

    private static final long serialVersionUID = 1L;


    /**
     * 用户唯一标识
     */
    @NotBlank(message = "登录标识不能为空")
    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
